{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-30T03:13:37.601Z", "updatedAt": "2025-07-30T03:13:37.607Z", "resourceCount": 3}, "resources": [{"id": "design-management-workflow", "source": "project", "protocol": "execution", "name": "Design Management Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ui-design-manager/execution/design-management-workflow.execution.md", "metadata": {"createdAt": "2025-07-30T03:13:37.604Z", "updatedAt": "2025-07-30T03:13:37.604Z", "scannedAt": "2025-07-30T03:13:37.604Z", "path": "role/ui-design-manager/execution/design-management-workflow.execution.md"}}, {"id": "design-leadership", "source": "project", "protocol": "thought", "name": "Design Leadership 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ui-design-manager/thought/design-leadership.thought.md", "metadata": {"createdAt": "2025-07-30T03:13:37.606Z", "updatedAt": "2025-07-30T03:13:37.606Z", "scannedAt": "2025-07-30T03:13:37.606Z", "path": "role/ui-design-manager/thought/design-leadership.thought.md"}}, {"id": "ui-design-manager", "source": "project", "protocol": "role", "name": "Ui Design Manager 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ui-design-manager/ui-design-manager.role.md", "metadata": {"createdAt": "2025-07-30T03:13:37.607Z", "updatedAt": "2025-07-30T03:13:37.607Z", "scannedAt": "2025-07-30T03:13:37.607Z", "path": "role/ui-design-manager/ui-design-manager.role.md"}}], "stats": {"totalResources": 3, "byProtocol": {"execution": 1, "thought": 1, "role": 1}, "bySource": {"project": 3}}}