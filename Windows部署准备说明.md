# 🪟 Windows 系统部署准备说明

## 🎯 三种方式准备上传文件

### 方式一：PowerShell 脚本（推荐）
```powershell
# 在项目根目录打开 PowerShell
.\scripts\prepare-upload.ps1
```

**高级选项：**
```powershell
# 包含构建后的文件（减少服务器编译时间）
.\scripts\prepare-upload.ps1 -BuildFirst

# 包含 node_modules（文件会很大，但部署更快）
.\scripts\prepare-upload.ps1 -IncludeNodeModules

# 两个选项都包含
.\scripts\prepare-upload.ps1 -BuildFirst -IncludeNodeModules
```

### 方式二：批处理脚本
```cmd
# 在项目根目录打开命令提示符
scripts\prepare-upload.bat
```

### 方式三：手动准备
如果脚本无法运行，可以手动创建文件夹：

```
📁 upload-package/
├── 📁 backend/
│   ├── 📁 src/                    # 复制整个 src 目录
│   ├── 📁 prisma/                 # 复制整个 prisma 目录
│   ├── 📄 package.json            # 复制文件
│   ├── 📄 package-lock.json       # 复制文件（如果存在）
│   ├── 📄 tsconfig.json           # 复制文件
│   └── 📄 .env.example            # 复制文件
├── 📁 frontend/
│   ├── 📁 src/                    # 复制整个 src 目录
│   ├── 📁 public/                 # 复制整个 public 目录
│   ├── 📄 package.json            # 复制文件
│   ├── 📄 package-lock.json       # 复制文件（如果存在）
│   ├── 📄 vite.config.ts          # 复制文件
│   ├── 📄 tsconfig.json           # 复制文件
│   └── 📄 index.html              # 复制文件
├── 📁 scripts/
│   ├── 📄 bt-deploy-helper.sh     # 复制文件
│   └── 📄 deployment-check.sh     # 复制文件
├── 📁 docs/
│   ├── 📄 宝塔面板部署指南.md
│   └── 📄 宝塔面板故障排除指南.md
├── 📄 ecosystem.config.js         # 手动创建（见下方内容）
├── 📄 DEPLOY.md                   # 手动创建部署说明
└── 📄 README.md                   # 复制文件（如果存在）
```

## 📄 手动创建的配置文件

### ecosystem.config.js
```javascript
module.exports = {
  apps: [{
    name: 'game-boost-backend',
    script: './backend/dist/index.js',
    cwd: '/www/wwwroot/game-boost',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    restart_delay: 4000
  }]
}
```

## 📦 压缩和上传

### 1. 压缩文件
- **Windows 11/10**: 右键点击 `upload-package` 文件夹 → 压缩为 ZIP 文件
- **7-Zip**: 右键 → 7-Zip → 添加到压缩文件
- **WinRAR**: 右键 → 添加到压缩文件

### 2. 上传到宝塔面板
1. 登录宝塔面板
2. 网站 → 添加站点
   - 域名：`your-domain.com`
   - 根目录：`/www/wwwroot/game-boost`
3. 文件 → 进入网站根目录
4. 上传 ZIP 文件
5. 解压文件

## 🚀 部署流程

### 1. 宝塔面板环境准备
在软件商店安装：
- [x] **Node.js版本管理器** → 安装 Node.js 16.x 或 18.x
- [x] **MySQL** → 8.0+
- [x] **Nginx** → 1.18+
- [x] **PM2管理器**

### 2. 运行自动部署脚本
在宝塔面板 → 终端，执行：
```bash
cd /www/wwwroot/game-boost
chmod +x scripts/bt-deploy-helper.sh
sudo bash scripts/bt-deploy-helper.sh
```

### 3. 按提示输入配置
- 域名（如：example.com）
- 数据库密码
- 确认配置信息

### 4. 等待部署完成
脚本会自动：
- ✅ 创建数据库和用户
- ✅ 安装后端依赖并构建
- ✅ 安装前端依赖并构建
- ✅ 配置 PM2 进程管理
- ✅ 配置 Nginx 反向代理

### 5. 验证部署
```bash
bash scripts/deployment-check.sh your-domain.com
```

## 🔧 常见问题解决

### PowerShell 执行策略问题
如果提示"无法加载文件，因为在此系统上禁止运行脚本"：

```powershell
# 临时允许执行脚本
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 运行脚本
.\scripts\prepare-upload.ps1

# 恢复原设置（可选）
Set-ExecutionPolicy -ExecutionPolicy Restricted -Scope CurrentUser
```

### 文件路径问题
确保在项目根目录运行脚本：
```cmd
# 检查当前目录
dir

# 应该能看到 backend、frontend、scripts 等文件夹
```

### 压缩文件过大
如果文件太大，可以：
1. 不包含 `node_modules`（推荐）
2. 使用 `.gitignore` 排除不必要的文件
3. 分别压缩 backend 和 frontend

## 📊 文件大小参考

| 包含内容 | 大小估算 | 上传时间 | 部署时间 |
|---------|---------|---------|---------|
| 仅源码 | 5-20 MB | 快 | 中等（需编译） |
| 源码 + 构建文件 | 20-50 MB | 中等 | 快 |
| 源码 + node_modules | 200-500 MB | 慢 | 很快 |
| 全部文件 | 500+ MB | 很慢 | 最快 |

**推荐**：仅上传源码，让服务器自动编译，平衡上传时间和部署复杂度。

## 🎯 快速开始

1. **运行准备脚本**：
   ```powershell
   .\scripts\prepare-upload.ps1
   ```

2. **压缩文件**：
   右键 `upload-package` → 压缩为 ZIP

3. **上传到宝塔**：
   上传 ZIP 到 `/www/wwwroot/game-boost/` 并解压

4. **运行部署**：
   ```bash
   sudo bash scripts/bt-deploy-helper.sh
   ```

5. **访问网站**：
   `http://your-domain.com`

就这么简单！🎉
