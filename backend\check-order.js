const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkOrder() {
  try {
    const order = await prisma.order.findFirst({
      orderBy: { createdAt: 'desc' }
    });

    console.log('最新订单数据:');
    console.log('ID:', order.id);
    console.log('orderNo:', order.orderNo);
    console.log('customerContact:', order.customerContact);
    console.log('requirements:', order.requirements);
    console.log('details:', JSON.stringify(order.details, null, 2));
    console.log('formData:', JSON.stringify(order.formData, null, 2));
  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkOrder();
