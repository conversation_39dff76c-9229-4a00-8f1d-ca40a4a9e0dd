// 快速测试服务器
const http = require('http');
const url = require('url');

console.log('Starting quick server...');

// 模拟数据
const mockGameFields = [
  {
    id: 'field_ys_001',
    gameId: 'cmdfek1h70004ayewoptwvqtk',
    fieldKey: 'customer_name',
    fieldLabel: '客户姓名',
    fieldType: 'TEXT',
    isRequired: true,
    placeholder: '请输入客户姓名',
    sortOrder: 1,
    isActive: true,
    options: null,
    config: null
  },
  {
    id: 'field_ys_002',
    gameId: 'cmdfek1h70004ayewoptwvqtk',
    fieldKey: 'customer_contact',
    fieldLabel: '联系方式',
    fieldType: 'TEXT',
    isRequired: false,
    placeholder: '请输入手机号或QQ号',
    sortOrder: 2,
    isActive: true,
    options: null,
    config: null
  },
  {
    id: 'field_ys_003',
    gameId: 'cmdfek1h70004ayewoptwvqtk',
    fieldKey: 'game_account',
    fieldLabel: '游戏账号',
    fieldType: 'TEXT',
    isRequired: true,
    placeholder: '请输入游戏UID',
    sortOrder: 3,
    isActive: true,
    options: null,
    config: null
  },
  {
    id: 'field_ys_004',
    gameId: 'cmdfek1h70004ayewoptwvqtk',
    fieldKey: 'server_region',
    fieldLabel: '服务器',
    fieldType: 'SELECT',
    isRequired: true,
    placeholder: '请选择服务器',
    sortOrder: 4,
    isActive: true,
    options: ['天空岛', '世界树', '美服', '欧服', '亚服'],
    config: null
  },
  {
    id: 'field_ys_005',
    gameId: 'cmdfek1h70004ayewoptwvqtk',
    fieldKey: 'price',
    fieldLabel: '订单价格',
    fieldType: 'NUMBER',
    isRequired: true,
    placeholder: '请输入订单价格',
    sortOrder: 5,
    isActive: true,
    options: null,
    config: { min: 1, max: 10000, step: 1 }
  }
];

const mockGames = [
  {
    id: 'cmdfek1h70004ayewoptwvqtk',
    name: 'ys',
    displayName: '原神',
    description: '',
    icon: null,
    isActive: true,
    sortOrder: 0
  },
  {
    id: 'cmdh2sgmz0011texcae9hsjmj',
    name: 'mc',
    displayName: '鸣潮',
    description: '',
    icon: null,
    isActive: true,
    sortOrder: 1
  },
  {
    id: 'game_wzry_001',
    name: 'wzry',
    displayName: '王者荣耀',
    description: '',
    icon: null,
    isActive: true,
    sortOrder: 2
  }
];

const server = http.createServer((req, res) => {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;
  
  console.log(`${req.method} ${pathname}`);

  // 路由处理
  if (pathname === '/api/v1/games/active') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      data: mockGames,
      message: '获取活跃游戏成功'
    }));
    return;
  }

  if (pathname.startsWith('/api/v1/game-form-fields/game/') && pathname.endsWith('/active')) {
    const gameId = pathname.split('/')[5]; // 提取gameId
    console.log('Requested gameId:', gameId);
    
    const fields = mockGameFields.filter(field => field.gameId === gameId);
    
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      data: fields,
      message: '获取游戏表单字段成功'
    }));
    return;
  }

  if (pathname === '/api/v1/game-form-fields/field-types') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      data: [
        { value: 'TEXT', label: '文本输入', description: '单行文本输入框' },
        { value: 'TEXTAREA', label: '多行文本', description: '多行文本输入框' },
        { value: 'SELECT', label: '下拉选择', description: '下拉选择框' },
        { value: 'CHECKBOX', label: '复选框', description: '复选框' },
        { value: 'NUMBER', label: '数字输入', description: '数字输入框' },
        { value: 'PASSWORD', label: '密码输入', description: '密码输入框' },
        { value: 'IMAGE', label: '图片上传', description: '图片上传组件' }
      ],
      message: '获取字段类型成功'
    }));
    return;
  }

  // 404
  res.writeHead(404, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({
    success: false,
    message: '接口不存在',
    data: null
  }));
});

const PORT = 3000;
server.listen(PORT, () => {
  console.log(`Quick server running on http://localhost:${PORT}`);
  console.log('Available endpoints:');
  console.log('- GET /api/v1/games/active');
  console.log('- GET /api/v1/game-form-fields/game/:gameId/active');
  console.log('- GET /api/v1/game-form-fields/field-types');
});

process.on('SIGINT', () => {
  console.log('\nShutting down server...');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});
