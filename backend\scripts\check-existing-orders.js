const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkExistingOrders() {
  try {
    console.log('🔍 检查现有订单...');
    
    // 查找鸣潮订单
    const mcOrders = await prisma.order.findMany({
      where: { gameType: 'mc' },
      include: {
        template: {
          select: {
            id: true,
            name: true,
            fields: true,
            displayConfig: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    });
    
    console.log(`找到 ${mcOrders.length} 个鸣潮订单`);
    
    mcOrders.forEach((order, index) => {
      console.log(`\n订单 ${index + 1}:`);
      console.log('  订单号:', order.orderNo);
      console.log('  客户姓名:', order.customerName);
      console.log('  游戏类型:', order.gameType);
      console.log('  模板ID:', order.templateId);
      console.log('  formData存在:', !!order.formData);
      
      if (order.formData) {
        console.log('  formData内容:');
        Object.entries(order.formData).forEach(([key, value]) => {
          console.log(`    ${key}: ${value}`);
        });
        
        // 检查每日签到字段
        if (order.formData.dailyCheckin !== undefined) {
          console.log('  ✅ 包含每日签到字段:', order.formData.dailyCheckin);
        } else {
          console.log('  ❌ 缺少每日签到字段');
        }
      }
      
      if (order.template) {
        console.log('  关联模板:', order.template.name);
        const fields = Array.isArray(order.template.fields) ? order.template.fields : JSON.parse(order.template.fields);
        const dailyCheckinField = fields.find(f => f.name === 'dailyCheckin' || f.label.includes('每日签到'));
        if (dailyCheckinField) {
          console.log('  ✅ 模板包含每日签到字段:', dailyCheckinField.label);
        } else {
          console.log('  ❌ 模板缺少每日签到字段');
        }
      }
    });
    
    // 如果没有鸣潮订单，检查鸣潮模板
    if (mcOrders.length === 0) {
      console.log('\n没有找到鸣潮订单，检查鸣潮模板...');
      
      const mcTemplate = await prisma.orderTemplate.findFirst({
        where: { gameType: 'mc' }
      });
      
      if (mcTemplate) {
        console.log('✅ 找到鸣潮模板:', mcTemplate.name);
        const fields = Array.isArray(mcTemplate.fields) ? mcTemplate.fields : JSON.parse(mcTemplate.fields);
        console.log('模板字段:', fields.map(f => f.label).join(', '));
        
        const dailyCheckinField = fields.find(f => f.name === 'dailyCheckin' || f.label.includes('每日签到'));
        if (dailyCheckinField) {
          console.log('✅ 模板包含每日签到字段:', dailyCheckinField.label);
        } else {
          console.log('❌ 模板缺少每日签到字段');
        }
      } else {
        console.log('❌ 未找到鸣潮模板');
      }
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkExistingOrders();
