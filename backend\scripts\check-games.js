const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkGames() {
  try {
    console.log('🔍 检查游戏数据...');
    
    // 检查活跃游戏
    const activeGames = await prisma.game.findMany({
      where: { isActive: true },
      include: {
        _count: {
          select: {
            formFields: true
          }
        }
      }
    });
    
    console.log(`✅ 活跃游戏数量: ${activeGames.length}`);
    activeGames.forEach(game => {
      console.log(`  - ${game.displayName} (${game.name}): ${game._count.formFields} 个字段`);
    });
    
    if (activeGames.length === 0) {
      console.log('\n⚠️  没有找到活跃游戏，检查所有游戏...');
      const allGames = await prisma.game.findMany();
      console.log(`总游戏数量: ${allGames.length}`);
      allGames.forEach(game => {
        console.log(`  - ${game.displayName} (${game.name}): 活跃=${game.isActive}`);
      });
      
      if (allGames.length === 0) {
        console.log('\n❌ 数据库中没有任何游戏数据！');
        console.log('请运行以下命令添加游戏数据:');
        console.log('  npm run seed');
      }
    }
    
    // 检查表单字段
    console.log('\n🔍 检查表单字段...');
    const formFields = await prisma.gameFormField.findMany({
      where: { isActive: true },
      include: {
        game: {
          select: {
            name: true,
            displayName: true
          }
        }
      }
    });
    
    console.log(`✅ 活跃表单字段数量: ${formFields.length}`);
    const fieldsByGame = {};
    formFields.forEach(field => {
      const gameName = field.game.displayName;
      if (!fieldsByGame[gameName]) {
        fieldsByGame[gameName] = [];
      }
      fieldsByGame[gameName].push(field.fieldLabel);
    });
    
    Object.keys(fieldsByGame).forEach(gameName => {
      console.log(`  ${gameName}:`);
      fieldsByGame[gameName].forEach(fieldLabel => {
        console.log(`    - ${fieldLabel}`);
      });
    });
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkGames();
