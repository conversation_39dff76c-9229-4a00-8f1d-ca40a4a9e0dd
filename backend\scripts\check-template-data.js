/**
 * 检查订单模板数据的脚本
 * 用于诊断模板字段标签不一致的问题
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkTemplateData() {
  try {
    console.log('🔍 开始检查订单模板数据...\n');

    // 获取所有活跃的模板
    const activeTemplates = await prisma.orderTemplate.findMany({
      where: {
        status: 'ACTIVE'
      },
      orderBy: {
        gameType: 'asc'
      }
    });

    console.log(`📊 找到 ${activeTemplates.length} 个活跃模板:\n`);

    for (const template of activeTemplates) {
      console.log(`🎮 游戏类型: ${template.gameType}`);
      console.log(`📝 模板名称: ${template.name}`);
      console.log(`🆔 模板ID: ${template.id}`);
      console.log(`📅 创建时间: ${template.createdAt}`);
      console.log(`📅 更新时间: ${template.updatedAt}`);
      
      // 检查字段配置
      if (template.fields && Array.isArray(template.fields)) {
        console.log(`🔧 字段数量: ${template.fields.length}`);
        console.log('📋 字段详情:');
        
        template.fields.forEach((field, index) => {
          console.log(`  ${index + 1}. ${field.label || '无标签'} (${field.name || '无名称'})`);
          console.log(`     类型: ${field.type || '未定义'}`);
          console.log(`     必填: ${field.required ? '是' : '否'}`);
          
          // 检查是否有异常的字段标签
          if (field.label && (
            field.label === '123' || 
            field.label === '测试1' || 
            field.label === '你好' ||
            field.label.length < 2 ||
            /^\d+$/.test(field.label)
          )) {
            console.log(`     ⚠️  异常标签: "${field.label}"`);
          }
          
          if (!field.name || !field.label || !field.type) {
            console.log(`     ❌ 字段配置不完整`);
          }
        });
      } else {
        console.log('❌ 字段配置异常或为空');
      }
      
      console.log('─'.repeat(50));
    }

    // 检查是否有使用这些模板的订单
    console.log('\n📦 检查订单使用情况:');
    
    for (const template of activeTemplates) {
      const orderCount = await prisma.order.count({
        where: {
          templateId: template.id
        }
      });
      
      console.log(`${template.gameType} (${template.name}): ${orderCount} 个订单`);
      
      if (orderCount > 0) {
        // 获取最近的几个订单样本
        const sampleOrders = await prisma.order.findMany({
          where: {
            templateId: template.id
          },
          select: {
            id: true,
            customerName: true,
            formData: true,
            createdAt: true
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 2
        });
        
        console.log(`  最近订单样本:`);
        sampleOrders.forEach(order => {
          console.log(`    订单 ${order.id}: ${order.customerName || '无客户名'}`);
          if (order.formData) {
            console.log(`    表单数据: ${JSON.stringify(order.formData).substring(0, 100)}...`);
          }
        });
      }
    }

    // 检查是否有孤儿订单（没有模板或模板已删除）
    console.log('\n🔍 检查孤儿订单:');
    
    const orphanOrders = await prisma.order.findMany({
      where: {
        OR: [
          { templateId: null },
          {
            templateId: {
              notIn: activeTemplates.map(t => t.id)
            }
          }
        ]
      },
      select: {
        id: true,
        gameType: true,
        customerName: true,
        templateId: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    });

    if (orphanOrders.length > 0) {
      console.log(`⚠️  找到 ${orphanOrders.length} 个孤儿订单:`);
      orphanOrders.forEach(order => {
        console.log(`  订单 ${order.id}: ${order.gameType} - ${order.customerName || '无客户名'}`);
        console.log(`    模板ID: ${order.templateId || '无'}`);
      });
    } else {
      console.log('✅ 没有发现孤儿订单');
    }

    console.log('\n✅ 模板数据检查完成');

  } catch (error) {
    console.error('❌ 检查模板数据时出错:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行检查
checkTemplateData();
