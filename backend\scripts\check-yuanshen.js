const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkYuanShenFields() {
  try {
    console.log('🔍 检查原神游戏的表单字段...');
    
    const yuanshenGame = await prisma.game.findFirst({
      where: { name: 'ys' },
      include: {
        formFields: {
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' }
        }
      }
    });
    
    if (yuanshenGame) {
      console.log('✅ 原神游戏信息:');
      console.log('- ID:', yuanshenGame.id);
      console.log('- 名称:', yuanshenGame.displayName);
      console.log('- 字段数量:', yuanshenGame.formFields.length);
      
      console.log('\n📋 表单字段详情:');
      yuanshenGame.formFields.forEach((field, index) => {
        console.log(`${index + 1}. ${field.fieldLabel} (${field.fieldKey})`);
        console.log(`   类型: ${field.fieldType}`);
        console.log(`   必需: ${field.isRequired}`);
        if (field.options) {
          console.log(`   选项: ${field.options}`);
        }
        console.log('');
      });
    } else {
      console.log('❌ 未找到原神游戏');
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkYuanShenFields();
