const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testGameFormFieldsAPI() {
  try {
    console.log('🧪 测试游戏表单字段API...');
    
    // 获取原神游戏
    const yuanshenGame = await prisma.game.findFirst({
      where: { name: 'ys' }
    });
    
    if (!yuanshenGame) {
      console.log('❌ 未找到原神游戏');
      return;
    }
    
    console.log('✅ 找到原神游戏:');
    console.log('- ID:', yuanshenGame.id);
    console.log('- 名称:', yuanshenGame.displayName);
    
    // 测试获取活跃字段
    console.log('\n🔍 测试获取活跃字段...');
    const activeFields = await prisma.gameFormField.findMany({
      where: {
        gameId: yuanshenGame.id,
        isActive: true
      },
      orderBy: { sortOrder: 'asc' }
    });
    
    console.log(`📋 找到 ${activeFields.length} 个活跃字段:`);
    activeFields.forEach((field, index) => {
      console.log(`${index + 1}. ${field.fieldLabel} (${field.fieldKey})`);
      console.log(`   类型: ${field.fieldType}`);
      console.log(`   必需: ${field.isRequired}`);
      console.log(`   排序: ${field.sortOrder}`);
      console.log('');
    });
    
    // 模拟API响应格式
    console.log('🔄 模拟API响应格式:');
    const apiResponse = activeFields.map(field => ({
      id: field.id,
      gameId: field.gameId,
      fieldKey: field.fieldKey,
      fieldLabel: field.fieldLabel,
      fieldType: field.fieldType,
      isRequired: field.isRequired,
      placeholder: field.placeholder,
      sortOrder: field.sortOrder,
      options: field.options,
      config: field.config,
      isActive: field.isActive,
      createdAt: field.createdAt.toISOString(),
      updatedAt: field.updatedAt.toISOString()
    }));
    
    console.log('📤 API响应数据:');
    console.log(JSON.stringify(apiResponse, null, 2));
    
    // 检查是否有游戏密码字段
    const passwordField = activeFields.find(field => 
      field.fieldKey === 'game_password' || 
      field.fieldLabel.includes('密码')
    );
    
    if (passwordField) {
      console.log('✅ 找到游戏密码字段:');
      console.log('- 字段键:', passwordField.fieldKey);
      console.log('- 字段标签:', passwordField.fieldLabel);
      console.log('- 字段类型:', passwordField.fieldType);
      console.log('- 是否必需:', passwordField.isRequired);
    } else {
      console.log('❌ 未找到游戏密码字段！');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testGameFormFieldsAPI();
