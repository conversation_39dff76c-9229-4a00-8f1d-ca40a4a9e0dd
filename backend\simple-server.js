console.log('Starting simple server...');

const express = require('express');
const cors = require('cors');
const { PrismaClient } = require('@prisma/client');

console.log('Creating Express app...');
const app = express();

console.log('Creating Prisma client...');
const prisma = new PrismaClient();

console.log('Setting up middleware...');
app.use(cors());
app.use(express.json());

// 测试API端点
app.get('/api/v1/game-form-fields/game/:gameId/active', async (req, res) => {
  try {
    console.log('Received request for gameId:', req.params.gameId);
    
    const { gameId } = req.params;
    
    const fields = await prisma.gameFormField.findMany({
      where: {
        gameId: gameId,
        isActive: true
      },
      orderBy: {
        sortOrder: 'asc'
      }
    });
    
    console.log('Found fields:', fields.length);
    
    res.json({
      success: true,
      data: fields,
      message: '获取游戏表单字段成功'
    });
    
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({
      success: false,
      message: '数据库操作失败',
      data: null
    });
  }
});

// 获取活跃游戏
app.get('/api/v1/games/active', async (req, res) => {
  try {
    const games = await prisma.game.findMany({
      where: {
        isActive: true
      },
      orderBy: {
        sortOrder: 'asc'
      }
    });
    
    res.json({
      success: true,
      data: games,
      message: '获取活跃游戏成功'
    });
    
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({
      success: false,
      message: '数据库操作失败',
      data: null
    });
  }
});

const PORT = 3000;
app.listen(PORT, () => {
  console.log(`Simple server running on http://localhost:${PORT}`);
});

process.on('SIGINT', async () => {
  await prisma.$disconnect();
  process.exit(0);
});
