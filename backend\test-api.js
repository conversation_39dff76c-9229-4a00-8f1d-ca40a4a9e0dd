const axios = require('axios');

async function testAPI() {
  try {
    console.log('🔍 测试API访问...');
    
    // 首先测试登录获取token
    console.log('1. 测试登录...');
    const loginResponse = await axios.post('http://localhost:3000/api/v1/auth/login', {
      username: 'admin',
      password: '123456'
    });

    console.log('✅ 登录成功');
    console.log('登录响应:', loginResponse.data);
    const token = loginResponse.data.data?.token || loginResponse.data.token;
    if (token) {
      console.log('Token:', token.substring(0, 20) + '...');
    } else {
      console.log('❌ 未找到token');
      return;
    }

    // 测试获取游戏列表
    console.log('\n2. 测试获取游戏列表...');
    const gamesResponse = await axios.get('http://localhost:3000/api/v1/games', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('✅ 游戏API调用成功');
    const items = gamesResponse.data.data?.items || gamesResponse.data.items;
    console.log('游戏数量:', items?.length || 0);

    // 测试创建游戏
    console.log('\n3. 测试创建游戏...');
    const createGameData = {
      name: 'test-game',
      displayName: '测试游戏',
      description: '这是一个测试游戏',
      icon: '',
      isActive: true,
      sortOrder: 0
    };

    try {
      const createResponse = await axios.post('http://localhost:3000/api/v1/games', createGameData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      console.log('✅ 创建游戏成功:', createResponse.data);
    } catch (createError) {
      console.log('❌ 创建游戏失败:', createError.response?.data || createError.message);
    }
    
  } catch (error) {
    console.error('❌ API测试失败:', error.response?.data || error.message);
  }
}

testAPI();
