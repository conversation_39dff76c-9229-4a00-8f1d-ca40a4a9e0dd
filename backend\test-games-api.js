const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testGamesAPI() {
  try {
    console.log('🔍 测试游戏API...');
    
    // 直接查询数据库
    const games = await prisma.game.findMany({
      include: {
        _count: {
          select: {
            orders: true,
            employeeSkills: true,
            formFields: true,
            ranks: true
          }
        },
        ranks: true
      },
      orderBy: {
        sortOrder: 'asc'
      }
    });
    
    console.log('📊 数据库中的游戏数据:');
    console.log(`总数: ${games.length}`);
    
    games.forEach((game, index) => {
      console.log(`${index + 1}. ${game.displayName} (${game.name})`);
      console.log(`   - ID: ${game.id}`);
      console.log(`   - 描述: ${game.description}`);
      console.log(`   - 状态: ${game.isActive ? '启用' : '禁用'}`);
      console.log(`   - 排序: ${game.sortOrder}`);
      console.log(`   - 段位数: ${game.ranks?.length || 0}`);
      console.log(`   - 订单数: ${game._count?.orders || 0}`);
      console.log(`   - 表单字段数: ${game._count?.formFields || 0}`);
      console.log('');
    });
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testGamesAPI();
