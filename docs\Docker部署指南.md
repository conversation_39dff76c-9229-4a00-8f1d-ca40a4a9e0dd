# 🐳 Docker 部署指南 - 王者荣耀代练管理系统

## 📋 概述

除了宝塔面板部署，本项目还提供了完整的Docker容器化部署方案，适合有Docker经验的用户或需要快速部署的场景。

## 🎯 Docker部署优势

### 相比宝塔部署的优势
- ✅ **环境一致性**: 开发、测试、生产环境完全一致
- ✅ **快速部署**: 一键启动所有服务
- ✅ **服务隔离**: 各服务独立运行，互不影响
- ✅ **版本管理**: 支持多版本并存和快速回滚
- ✅ **资源控制**: 精确控制每个服务的资源使用

### 适用场景
- 🔧 开发环境快速搭建
- 🚀 测试环境部署
- 📦 微服务架构部署
- 🔄 CI/CD自动化部署

## 🛠️ 部署要求

### 系统要求
- **操作系统**: Linux (推荐Ubuntu 20.04+/CentOS 8+)
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **内存**: 4GB以上
- **磁盘**: 20GB以上

### 端口要求
- **80**: 前端Web服务
- **3000**: 后端API服务
- **3306**: MySQL数据库
- **6379**: Redis缓存
- **8080**: Nginx反向代理

## 🚀 快速部署

### 1. 安装Docker和Docker Compose

#### Ubuntu/Debian
```bash
# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker
```

#### CentOS/RHEL
```bash
# 安装Docker
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install -y docker-ce docker-ce-cli containerd.io

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker
```

### 2. 部署项目

```bash
# 1. 克隆项目
git clone <your-repo-url>
cd game-boost-system

# 2. 进入Docker目录
cd docker

# 3. 配置环境变量
cp .env.example .env
nano .env

# 4. 启动所有服务
docker-compose up -d

# 5. 查看服务状态
docker-compose ps
```

## ⚙️ 配置说明

### 环境变量配置 (.env)
```bash
# 数据库配置
MYSQL_ROOT_PASSWORD=root123456
MYSQL_DATABASE=game_boost_db
MYSQL_USER=gameuser
MYSQL_PASSWORD=gamepass123

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-here

# 域名配置
DOMAIN=your-domain.com

# Redis配置
REDIS_PASSWORD=

# 邮件配置(可选)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
```

### Docker Compose 服务说明

#### MySQL数据库
```yaml
mysql:
  image: mysql:8.0
  environment:
    MYSQL_ROOT_PASSWORD: root123456
    MYSQL_DATABASE: game_boost_db
    MYSQL_USER: gameuser
    MYSQL_PASSWORD: gamepass123
  ports:
    - "3306:3306"
  volumes:
    - mysql_data:/var/lib/mysql
```

#### Redis缓存
```yaml
redis:
  image: redis:7-alpine
  ports:
    - "6379:6379"
  volumes:
    - redis_data:/data
  command: redis-server --appendonly yes
```

#### 后端API
```yaml
backend:
  build: ../backend
  ports:
    - "3000:3000"
  environment:
    NODE_ENV: production
    DATABASE_URL: mysql://gameuser:gamepass123@mysql:3306/game_boost_db
    REDIS_HOST: redis
    JWT_SECRET: your-super-secret-jwt-key-here
  depends_on:
    - mysql
    - redis
```

#### 前端Web
```yaml
frontend:
  build: ../frontend
  ports:
    - "80:80"
  depends_on:
    - backend
```

## 🔧 常用管理命令

### 服务管理
```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启特定服务
docker-compose restart backend

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f backend
```

### 数据管理
```bash
# 备份数据库
docker exec mysql mysqldump -u root -proot123456 game_boost_db > backup.sql

# 恢复数据库
docker exec -i mysql mysql -u root -proot123456 game_boost_db < backup.sql

# 进入数据库容器
docker exec -it mysql mysql -u root -proot123456

# 进入Redis容器
docker exec -it redis redis-cli
```

### 应用管理
```bash
# 查看后端日志
docker-compose logs -f backend

# 进入后端容器
docker exec -it game-boost-backend bash

# 重新构建镜像
docker-compose build --no-cache

# 更新应用
git pull
docker-compose build
docker-compose up -d
```

## 📊 监控和维护

### 健康检查
```bash
# 检查所有服务状态
docker-compose ps

# 检查API健康状态
curl http://localhost:3000/api/v1/health

# 检查前端访问
curl http://localhost/
```

### 性能监控
```bash
# 查看容器资源使用
docker stats

# 查看容器详细信息
docker inspect game-boost-backend

# 查看网络信息
docker network ls
docker network inspect docker_game-boost-network
```

### 日志管理
```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs backend

# 实时跟踪日志
docker-compose logs -f --tail=100 backend

# 清理日志
docker system prune -f
```

## 🔒 安全配置

### 生产环境安全建议
1. **修改默认密码**: 更改数据库和Redis密码
2. **使用HTTPS**: 配置SSL证书
3. **网络隔离**: 使用自定义网络
4. **资源限制**: 设置容器资源限制
5. **定期更新**: 保持镜像版本最新

### 防火墙配置
```bash
# 只开放必要端口
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

## 🚨 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看详细错误
   docker-compose logs service-name
   
   # 检查配置文件
   docker-compose config
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库容器状态
   docker-compose ps mysql
   
   # 查看数据库日志
   docker-compose logs mysql
   ```

3. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :3000
   
   # 修改docker-compose.yml中的端口映射
   ```

4. **磁盘空间不足**
   ```bash
   # 清理未使用的镜像和容器
   docker system prune -a
   
   # 清理数据卷
   docker volume prune
   ```

## 📈 性能优化

### 生产环境优化
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  backend:
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    restart: unless-stopped
    
  mysql:
    command: --innodb-buffer-pool-size=512M --max-connections=200
    
  redis:
    command: redis-server --maxmemory 256mb --maxmemory-policy allkeys-lru
```

## 🔄 CI/CD集成

### GitHub Actions示例
```yaml
name: Deploy to Production
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Deploy to server
        run: |
          ssh user@server "cd /path/to/project && git pull && docker-compose up -d --build"
```

## 📞 技术支持

### 获取帮助
- 查看Docker官方文档
- 检查项目GitHub Issues
- 联系技术支持团队

### 常用资源
- [Docker官方文档](https://docs.docker.com/)
- [Docker Compose文档](https://docs.docker.com/compose/)
- [项目GitHub仓库](https://github.com/your-repo)

---

**Docker部署为宝塔部署提供了强有力的替代方案，选择最适合您环境的部署方式！** 🐳
