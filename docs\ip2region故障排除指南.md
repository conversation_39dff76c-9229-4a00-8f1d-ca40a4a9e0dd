# 🌍 ip2region 离线IP库故障排除指南

## 📋 概述

ip2region是项目中用于离线IP地理位置查询的核心组件，支持高性能的本地IP地址解析。本指南帮助您解决ip2region相关的部署和使用问题。

## 🔍 快速诊断

### 一键检查脚本
```bash
# 检查ip2region状态
bash scripts/check-ip2region-status.sh

# 重新配置ip2region
bash scripts/setup-ip2region.sh
```

## 🚨 常见问题及解决方案

### 1. ip2region数据库文件不存在

#### 问题现象
- 后端启动时报错：`IP数据库文件不存在`
- API调用返回默认位置信息
- 日志显示：`IP地理位置服务未初始化`

#### 排查步骤
```bash
# 检查源文件是否存在
ls -la /www/wwwroot/game-boost/ip2region-master/data/ip2region.xdb

# 检查目标文件是否存在
ls -la /www/wwwroot/game-boost/backend/src/data/ip2region.xdb

# 检查文件大小
stat /www/wwwroot/game-boost/backend/src/data/ip2region.xdb
```

#### 解决方案

**方案1：重新配置ip2region**
```bash
cd /www/wwwroot/game-boost
bash scripts/setup-ip2region.sh
```

**方案2：手动复制文件**
```bash
# 创建目标目录
mkdir -p /www/wwwroot/game-boost/backend/src/data

# 复制数据库文件
cp /www/wwwroot/game-boost/ip2region-master/data/ip2region.xdb \
   /www/wwwroot/game-boost/backend/src/data/

# 设置权限
chmod 644 /www/wwwroot/game-boost/backend/src/data/ip2region.xdb
chown www:www /www/wwwroot/game-boost/backend/src/data/ip2region.xdb
```

### 2. Node.js绑定库缺失

#### 问题现象
- 后端启动时报错：`Cannot find module '../lib/index.js'`
- IP查询功能完全无法使用

#### 排查步骤
```bash
# 检查绑定库文件
ls -la /www/wwwroot/game-boost/backend/src/lib/index.js
ls -la /www/wwwroot/game-boost/backend/src/lib/package.json

# 检查源文件
ls -la /www/wwwroot/game-boost/ip2region-master/binding/nodejs/
```

#### 解决方案
```bash
# 创建目标目录
mkdir -p /www/wwwroot/game-boost/backend/src/lib

# 复制绑定库文件
cp /www/wwwroot/game-boost/ip2region-master/binding/nodejs/index.js \
   /www/wwwroot/game-boost/backend/src/lib/

cp /www/wwwroot/game-boost/ip2region-master/binding/nodejs/package.json \
   /www/wwwroot/game-boost/backend/src/lib/

# 设置权限
chmod 644 /www/wwwroot/game-boost/backend/src/lib/*
chown -R www:www /www/wwwroot/game-boost/backend/src/lib/
```

### 3. ip2region-master目录缺失

#### 问题现象
- 部署时提示：`ip2region源目录不存在`
- 无法找到ip2region相关文件

#### 解决方案

**方案1：重新上传ip2region-master**
1. 确保项目根目录包含完整的`ip2region-master`文件夹
2. 检查目录结构：
```
/www/wwwroot/game-boost/
├── ip2region-master/
│   ├── data/
│   │   └── ip2region.xdb
│   └── binding/
│       └── nodejs/
│           ├── index.js
│           └── package.json
```

**方案2：从官方下载**
```bash
cd /www/wwwroot/game-boost
wget https://github.com/lionsoul2014/ip2region/archive/master.zip
unzip master.zip
mv ip2region-master ip2region-master-backup
mv ip2region-master ip2region-master
```

### 4. IP查询返回"未知"

#### 问题现象
- 所有IP查询都返回"未知"位置
- 日志显示查询成功但结果为空

#### 排查步骤
```bash
# 测试ip2region功能
cd /www/wwwroot/game-boost
node -e "
const ip2region = require('./backend/src/lib/index.js');
const path = require('path');
const dbPath = path.join(__dirname, 'backend/src/data/ip2region.xdb');
const buffer = ip2region.loadContentFromFile(dbPath);
const searcher = ip2region.newWithBuffer(buffer);
searcher.search('*******').then(result => {
  console.log('测试结果:', result);
}).catch(err => {
  console.error('测试失败:', err);
});
"
```

#### 解决方案

**检查数据库文件完整性**
```bash
# 检查文件大小（正常应该在10MB左右）
ls -lh /www/wwwroot/game-boost/backend/src/data/ip2region.xdb

# 重新复制数据库文件
cp /www/wwwroot/game-boost/ip2region-master/data/ip2region.xdb \
   /www/wwwroot/game-boost/backend/src/data/ip2region.xdb
```

### 5. 性能问题

#### 问题现象
- IP查询响应缓慢
- 服务器内存使用率高
- 查询耗时超过100微秒

#### 解决方案

**优化1：确认使用内存缓存模式**
检查`backend/src/services/ipLocationService.ts`中的配置：
```typescript
// 确保使用内存缓存模式（性能最佳）
const buffer = ip2region.loadContentFromFile(dbPath);
this.searcher = ip2region.newWithBuffer(buffer);
```

**优化2：调整PM2配置**
```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'game-boost-backend',
    script: './backend/dist/index.js',
    instances: 1, // 单实例避免重复加载数据库
    max_memory_restart: '2G', // 增加内存限制
    // ...其他配置
  }]
}
```

### 6. 权限问题

#### 问题现象
- 文件访问被拒绝
- 无法读取ip2region.xdb文件

#### 解决方案
```bash
# 修复文件权限
chown -R www:www /www/wwwroot/game-boost/backend/src/data/
chown -R www:www /www/wwwroot/game-boost/backend/src/lib/
chmod 644 /www/wwwroot/game-boost/backend/src/data/ip2region.xdb
chmod 644 /www/wwwroot/game-boost/backend/src/lib/index.js
```

## 🔧 维护工具

### 定期检查脚本
```bash
#!/bin/bash
# ip2region-health-check.sh

PROJECT_DIR="/www/wwwroot/game-boost"
LOG_FILE="/var/log/ip2region-check.log"

echo "$(date): 开始ip2region健康检查" >> $LOG_FILE

# 检查文件存在性
if [ ! -f "$PROJECT_DIR/backend/src/data/ip2region.xdb" ]; then
    echo "$(date): 错误 - ip2region数据库文件不存在" >> $LOG_FILE
    exit 1
fi

# 检查文件大小
SIZE=$(stat -c%s "$PROJECT_DIR/backend/src/data/ip2region.xdb")
if [ $SIZE -lt 10000000 ]; then  # 小于10MB
    echo "$(date): 警告 - ip2region数据库文件大小异常: $SIZE bytes" >> $LOG_FILE
fi

# 功能测试
cd $PROJECT_DIR
TEST_RESULT=$(node -e "
const ip2region = require('./backend/src/lib/index.js');
const path = require('path');
try {
  const dbPath = path.join(__dirname, 'backend/src/data/ip2region.xdb');
  const buffer = ip2region.loadContentFromFile(dbPath);
  const searcher = ip2region.newWithBuffer(buffer);
  searcher.search('*******').then(result => {
    console.log(result.region ? 'OK' : 'FAIL');
  }).catch(() => console.log('FAIL'));
} catch(e) {
  console.log('FAIL');
}
" 2>/dev/null)

if [ "$TEST_RESULT" = "OK" ]; then
    echo "$(date): ip2region功能测试通过" >> $LOG_FILE
else
    echo "$(date): 错误 - ip2region功能测试失败" >> $LOG_FILE
    exit 1
fi

echo "$(date): ip2region健康检查完成" >> $LOG_FILE
```

### 数据库更新脚本
```bash
#!/bin/bash
# update-ip2region-db.sh

# 备份当前数据库
cp /www/wwwroot/game-boost/backend/src/data/ip2region.xdb \
   /www/wwwroot/game-boost/backend/src/data/ip2region.xdb.backup

# 下载最新数据库（如果有新版本）
# wget https://github.com/lionsoul2014/ip2region/raw/master/data/ip2region.xdb \
#      -O /www/wwwroot/game-boost/backend/src/data/ip2region.xdb.new

# 验证新数据库
# if [ -f "/www/wwwroot/game-boost/backend/src/data/ip2region.xdb.new" ]; then
#     mv /www/wwwroot/game-boost/backend/src/data/ip2region.xdb.new \
#        /www/wwwroot/game-boost/backend/src/data/ip2region.xdb
#     pm2 restart game-boost-backend
# fi
```

## 📊 监控指标

### 关键指标
- **数据库文件大小**: ~11MB
- **查询响应时间**: <50微秒
- **内存使用**: ~15MB (缓存模式)
- **查询成功率**: >99%

### 监控命令
```bash
# 查看ip2region相关日志
grep -i "ip.*location" /www/wwwroot/game-boost/logs/combined.log

# 监控查询性能
grep -i "ip地址解析成功" /www/wwwroot/game-boost/logs/combined.log | tail -10

# 检查错误
grep -i "ip地址解析失败" /www/wwwroot/game-boost/logs/err.log
```

## 📞 获取帮助

### 收集诊断信息
```bash
#!/bin/bash
# collect-ip2region-info.sh

echo "=== ip2region诊断信息 ===" > ip2region-diagnostic.txt
echo "时间: $(date)" >> ip2region-diagnostic.txt
echo "" >> ip2region-diagnostic.txt

echo "=== 文件检查 ===" >> ip2region-diagnostic.txt
ls -la /www/wwwroot/game-boost/backend/src/data/ip2region.xdb >> ip2region-diagnostic.txt
ls -la /www/wwwroot/game-boost/backend/src/lib/index.js >> ip2region-diagnostic.txt
ls -la /www/wwwroot/game-boost/ip2region-master/data/ip2region.xdb >> ip2region-diagnostic.txt

echo "" >> ip2region-diagnostic.txt
echo "=== 功能测试 ===" >> ip2region-diagnostic.txt
cd /www/wwwroot/game-boost
node -e "
const ip2region = require('./backend/src/lib/index.js');
const path = require('path');
try {
  const dbPath = path.join(__dirname, 'backend/src/data/ip2region.xdb');
  const buffer = ip2region.loadContentFromFile(dbPath);
  const searcher = ip2region.newWithBuffer(buffer);
  searcher.search('*******').then(result => {
    console.log('测试结果:', JSON.stringify(result, null, 2));
  }).catch(err => {
    console.error('测试失败:', err.message);
  });
} catch(e) {
  console.error('初始化失败:', e.message);
}
" >> ip2region-diagnostic.txt 2>&1

echo "诊断信息已保存到 ip2region-diagnostic.txt"
```

---

**记住**: ip2region是离线IP库，无需网络连接即可工作，确保数据库文件完整是关键！
