# 🚨 宝塔面板故障排除指南

## 📋 快速诊断

### 一键检查脚本
```bash
# 运行部署检查脚本
bash scripts/deployment-check.sh your-domain.com

# 查看服务状态
pm2 list
systemctl status nginx
systemctl status mysql
```

## 🔧 常见问题及解决方案

### 1. 后端服务无法启动

#### 问题现象
- PM2 显示服务状态为 `errored` 或 `stopped`
- 访问 API 返回 502 错误

#### 排查步骤
```bash
# 查看 PM2 日志
pm2 logs game-boost-backend

# 查看错误日志
tail -f /www/wwwroot/game-boost/logs/err.log

# 检查端口占用
netstat -tlnp | grep 3000
```

#### 常见原因及解决方案

**数据库连接失败**
```bash
# 检查数据库服务
systemctl status mysql

# 测试数据库连接
mysql -u game_boost_user -p game_boost_db

# 检查环境变量
cat /www/wwwroot/game-boost/backend/.env
```

**端口被占用**
```bash
# 查找占用进程
lsof -i :3000

# 杀死占用进程
kill -9 PID

# 重启服务
pm2 restart game-boost-backend
```

**依赖包问题**
```bash
cd /www/wwwroot/game-boost/backend
rm -rf node_modules package-lock.json
npm install
npm run build
pm2 restart game-boost-backend
```

### 2. 前端页面无法访问

#### 问题现象
- 访问网站显示 404 或 502 错误
- 页面空白或加载失败

#### 排查步骤
```bash
# 检查 Nginx 状态
systemctl status nginx

# 测试 Nginx 配置
nginx -t

# 查看 Nginx 错误日志
tail -f /www/wwwroot/logs/your-domain.com.error.log
```

#### 解决方案

**Nginx 配置错误**
```bash
# 重新生成配置文件
cat > /www/server/panel/vhost/nginx/your-domain.com.conf << 'EOF'
server {
    listen 80;
    server_name your-domain.com;
    root /www/wwwroot/game-boost/frontend/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://127.0.0.1:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
EOF

# 重载配置
nginx -s reload
```

**静态文件不存在**
```bash
cd /www/wwwroot/game-boost/frontend
npm run build
chown -R www:www dist/
```

### 3. 数据库连接问题

#### 问题现象
- 后端日志显示数据库连接错误
- API 请求返回数据库相关错误

#### 排查步骤
```bash
# 检查 MySQL 服务
systemctl status mysql

# 查看 MySQL 错误日志
tail -f /var/log/mysql/error.log

# 测试数据库连接
mysql -u game_boost_user -p
```

#### 解决方案

**MySQL 服务未启动**
```bash
systemctl start mysql
systemctl enable mysql
```

**用户权限问题**
```sql
-- 重新创建用户和授权
DROP USER IF EXISTS 'game_boost_user'@'localhost';
CREATE USER 'game_boost_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON game_boost_db.* TO 'game_boost_user'@'localhost';
FLUSH PRIVILEGES;
```

**数据库不存在**
```sql
CREATE DATABASE IF NOT EXISTS game_boost_db 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 4. SSL 证书问题

#### 问题现象
- HTTPS 访问失败
- 浏览器显示证书错误

#### 解决方案

**申请 Let's Encrypt 证书**
1. 在宝塔面板 → 网站 → SSL
2. 选择 Let's Encrypt
3. 输入域名并申请

**手动配置证书**
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_prefer_server_ciphers on;
}
```

### 5. 文件上传问题

#### 问题现象
- 文件上传失败
- 上传的文件无法访问

#### 解决方案

**检查上传目录权限**
```bash
mkdir -p /www/wwwroot/game-boost/uploads
chown -R www:www /www/wwwroot/game-boost/uploads
chmod -R 755 /www/wwwroot/game-boost/uploads
```

**检查 Nginx 文件大小限制**
```nginx
# 在 server 块中添加
client_max_body_size 10M;
```

**检查后端配置**
```bash
# 确认环境变量
grep UPLOAD_PATH /www/wwwroot/game-boost/backend/.env
```

### 6. 性能问题

#### 问题现象
- 网站访问缓慢
- 服务器资源使用率高

#### 排查步骤
```bash
# 查看系统资源
htop
df -h
free -h

# 查看进程资源使用
pm2 monit

# 查看网络连接
netstat -an | grep :80 | wc -l
```

#### 解决方案

**启用 PM2 集群模式**
```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'game-boost-backend',
    script: './backend/dist/index.js',
    instances: 'max',  // 使用所有CPU核心
    exec_mode: 'cluster'
  }]
}
```

**优化 Nginx 配置**
```nginx
# 启用 Gzip 压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript;

# 启用缓存
location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## 🔍 日志分析

### 重要日志位置
```bash
# 应用日志
/www/wwwroot/game-boost/logs/

# Nginx 日志
/www/wwwroot/logs/

# MySQL 日志
/var/log/mysql/

# 系统日志
/var/log/messages
/var/log/syslog
```

### 日志分析命令
```bash
# 查看实时日志
tail -f /www/wwwroot/game-boost/logs/combined.log

# 搜索错误
grep -i error /www/wwwroot/game-boost/logs/err.log

# 统计访问量
awk '{print $1}' /www/wwwroot/logs/access.log | sort | uniq -c | sort -nr

# 查看最近的错误
journalctl -u nginx -f
```

## 🛠️ 维护工具

### 自动重启脚本
```bash
#!/bin/bash
# auto-restart.sh

# 检查服务状态并自动重启
check_and_restart() {
    if ! pm2 list | grep -q "online.*game-boost-backend"; then
        echo "$(date): 后端服务异常，正在重启..."
        pm2 restart game-boost-backend
    fi
    
    if ! systemctl is-active --quiet nginx; then
        echo "$(date): Nginx 服务异常，正在重启..."
        systemctl restart nginx
    fi
}

check_and_restart
```

### 定时任务设置
```bash
# 添加到 crontab
crontab -e

# 每5分钟检查一次服务状态
*/5 * * * * /path/to/auto-restart.sh >> /var/log/auto-restart.log 2>&1

# 每天凌晨清理日志
0 0 * * * find /www/wwwroot/game-boost/logs -name "*.log" -mtime +7 -delete
```

## 📞 获取帮助

### 收集诊断信息
```bash
#!/bin/bash
# collect-info.sh

echo "=== 系统信息 ===" > diagnostic.txt
uname -a >> diagnostic.txt
cat /etc/os-release >> diagnostic.txt

echo -e "\n=== 服务状态 ===" >> diagnostic.txt
pm2 list >> diagnostic.txt
systemctl status nginx >> diagnostic.txt
systemctl status mysql >> diagnostic.txt

echo -e "\n=== 资源使用 ===" >> diagnostic.txt
free -h >> diagnostic.txt
df -h >> diagnostic.txt

echo -e "\n=== 最近错误 ===" >> diagnostic.txt
tail -n 50 /www/wwwroot/game-boost/logs/err.log >> diagnostic.txt

echo "诊断信息已保存到 diagnostic.txt"
```

### 联系支持
当遇到无法解决的问题时，请提供：
1. 错误现象描述
2. 错误日志内容
3. 系统环境信息
4. 已尝试的解决方案

---

**记住**: 在进行任何重要操作前，请先备份数据！
