# 🚀 宝塔面板部署指南

## 📋 项目概述

本项目是一个基于 Vue 3 + Node.js + MySQL 的游戏代练管理系统，包含：
- **前端**: Vue 3 + TypeScript + Element Plus
- **后端**: Node.js + Express + Prisma + MySQL
- **数据库**: MySQL 8.0+

## 🛠️ 环境要求

### 服务器配置
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **硬盘**: 20GB以上可用空间
- **操作系统**: CentOS 7+ / Ubuntu 18+ / Debian 9+

### 软件环境
- **宝塔面板**: 7.7.0+
- **Node.js**: 16.x 或 18.x
- **MySQL**: 8.0+
- **Nginx**: 1.18+
- **PM2**: 进程管理器

## 📦 第一步：安装宝塔面板

### 1.1 安装宝塔面板
```bash
# CentOS 安装命令
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh

# Ubuntu/Debian 安装命令
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh
```

### 1.2 登录宝塔面板
安装完成后，记录下：
- 面板地址：`http://你的服务器IP:8888`
- 用户名和密码

## 🔧 第二步：安装运行环境

### 2.1 安装必要软件
在宝塔面板 → 软件商店 → 运行环境，安装：

1. **Nginx** (1.18+)
2. **MySQL** (8.0+) 
3. **Node.js版本管理器** (安装 Node.js 16.x 或 18.x)
4. **PM2管理器** (进程管理)

### 2.2 配置 MySQL
1. 进入 MySQL 管理
2. 创建数据库：`game_boost_db`
3. 创建用户：`game_boost_user`
4. 设置密码并授权

```sql
CREATE DATABASE game_boost_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'game_boost_user'@'localhost' IDENTIFIED BY '你的密码';
GRANT ALL PRIVILEGES ON game_boost_db.* TO 'game_boost_user'@'localhost';
FLUSH PRIVILEGES;
```

## 📁 第三步：上传项目文件

### 3.1 创建网站目录
在宝塔面板 → 网站 → 添加站点：
- 域名：`你的域名.com`
- 根目录：`/www/wwwroot/game-boost`

### 3.2 上传项目文件
方式一：通过宝塔文件管理器上传压缩包
方式二：使用 Git 克隆（推荐）

```bash
# 进入网站根目录
cd /www/wwwroot/game-boost

# 克隆项目（如果有 Git 仓库）
git clone https://github.com/your-repo/game-boost.git .

# 或者直接上传项目文件到此目录
```

### 3.3 设置目录权限
```bash
# 设置正确的文件权限
chown -R www:www /www/wwwroot/game-boost
chmod -R 755 /www/wwwroot/game-boost
```

## ⚙️ 第四步：配置后端

### 4.1 安装后端依赖
```bash
cd /www/wwwroot/game-boost/backend
npm install
```

### 4.2 配置环境变量
创建 `.env` 文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件：
```env
# 数据库配置
DATABASE_URL="mysql://game_boost_user:你的密码@localhost:3306/game_boost_db"

# JWT 密钥
JWT_SECRET="your-super-secret-jwt-key-here"

# 服务器配置
PORT=3000
NODE_ENV=production

# 文件上传配置
UPLOAD_PATH="/www/wwwroot/game-boost/uploads"
MAX_FILE_SIZE=10485760

# 前端地址
FRONTEND_URL="https://你的域名.com"
```

### 4.3 初始化数据库
```bash
# 生成 Prisma 客户端
npx prisma generate

# 运行数据库迁移
npx prisma migrate deploy

# 初始化数据（可选）
npm run seed
```

### 4.4 构建后端
```bash
npm run build
```

## 🎨 第五步：配置前端

### 5.1 安装前端依赖
```bash
cd /www/wwwroot/game-boost/frontend
npm install
```

### 5.2 配置环境变量
创建 `.env.production` 文件：
```env
# API 基础地址
VITE_API_BASE_URL=https://你的域名.com/api
VITE_APP_TITLE=游戏代练管理系统

# 文件上传配置
VITE_UPLOAD_URL=https://你的域名.com/api/upload
```

### 5.3 构建前端
```bash
npm run build
```

构建完成后，`dist` 目录就是前端静态文件。

## 🔄 第六步：配置 PM2 进程管理

### 6.1 创建 PM2 配置文件
在项目根目录创建 `ecosystem.config.js`：
```javascript
module.exports = {
  apps: [{
    name: 'game-boost-backend',
    script: './backend/dist/index.js',
    cwd: '/www/wwwroot/game-boost',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G'
  }]
}
```

### 6.2 启动后端服务
```bash
# 创建日志目录
mkdir -p /www/wwwroot/game-boost/logs

# 启动服务
cd /www/wwwroot/game-boost
pm2 start ecosystem.config.js

# 设置开机自启
pm2 startup
pm2 save
```

## 🌐 第七步：配置 Nginx

### 7.1 网站配置
在宝塔面板 → 网站 → 你的站点 → 设置 → 配置文件，替换为：

```nginx
server {
    listen 80;
    listen 443 ssl http2;
    server_name 你的域名.com;
    
    # SSL 证书配置（如果有）
    # ssl_certificate /path/to/your/cert.pem;
    # ssl_certificate_key /path/to/your/key.pem;
    
    root /www/wwwroot/game-boost/frontend/dist;
    index index.html;
    
    # 前端静态文件
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "public, max-age=31536000";
    }
    
    # API 代理
    location /api/ {
        proxy_pass http://127.0.0.1:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket 支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 文件上传
    location /uploads/ {
        alias /www/wwwroot/game-boost/uploads/;
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }
    
    # 安全配置
    location ~ /\. {
        deny all;
    }
    
    # 文件大小限制
    client_max_body_size 10M;
}
```

### 7.2 重载 Nginx
```bash
nginx -t  # 测试配置
nginx -s reload  # 重载配置
```

## 🔒 第八步：安全配置

### 8.1 防火墙设置
在宝塔面板 → 安全：
- 开放端口：80, 443
- 关闭端口：3000 (后端端口不对外开放)
- 设置 SSH 端口（非 22）

### 8.2 SSL 证书
在宝塔面板 → 网站 → SSL：
- 申请 Let's Encrypt 免费证书
- 或上传自己的证书

### 8.3 定期备份
设置定期备份：
- 数据库备份：每日
- 网站文件备份：每周

## 📊 第九步：监控和维护

### 9.1 查看服务状态
```bash
# 查看 PM2 进程
pm2 list
pm2 logs game-boost-backend

# 查看系统资源
htop
df -h
```

### 9.2 常用维护命令
```bash
# 重启后端服务
pm2 restart game-boost-backend

# 更新代码
cd /www/wwwroot/game-boost
git pull
cd backend && npm run build
cd ../frontend && npm run build
pm2 restart game-boost-backend

# 查看日志
tail -f /www/wwwroot/game-boost/logs/combined.log
```

## 🚨 故障排除

### 常见问题

1. **后端启动失败**
   - 检查数据库连接
   - 查看 PM2 日志：`pm2 logs`

2. **前端页面空白**
   - 检查 Nginx 配置
   - 确认静态文件路径

3. **API 请求失败**
   - 检查 Nginx 代理配置
   - 确认后端服务运行状态

4. **数据库连接失败**
   - 检查 MySQL 服务状态
   - 验证数据库用户权限

### 日志位置
- Nginx 日志：`/www/wwwroot/logs/`
- PM2 日志：`/www/wwwroot/game-boost/logs/`
- MySQL 日志：宝塔面板 → MySQL → 日志

## ✅ 部署完成检查清单

- [ ] 宝塔面板安装完成
- [ ] Node.js、MySQL、Nginx 安装完成
- [ ] 数据库创建并配置完成
- [ ] 后端代码上传并构建完成
- [ ] 前端代码构建完成
- [ ] PM2 进程管理配置完成
- [ ] Nginx 反向代理配置完成
- [ ] SSL 证书配置完成（可选）
- [ ] 防火墙和安全设置完成
- [ ] 网站可以正常访问
- [ ] 管理后台和工作台功能正常

## 🎯 快速部署脚本

为了简化部署过程，您可以使用以下脚本：

### 一键部署脚本
创建 `deploy.sh` 文件：
```bash
#!/bin/bash
echo "🚀 开始部署游戏代练管理系统..."

# 设置变量
PROJECT_DIR="/www/wwwroot/game-boost"
DB_NAME="game_boost_db"
DB_USER="game_boost_user"
DB_PASS="your_password_here"

# 检查目录
if [ ! -d "$PROJECT_DIR" ]; then
    echo "❌ 项目目录不存在: $PROJECT_DIR"
    exit 1
fi

cd $PROJECT_DIR

# 安装后端依赖
echo "📦 安装后端依赖..."
cd backend
npm install

# 构建后端
echo "🔨 构建后端..."
npm run build

# 安装前端依赖
echo "📦 安装前端依赖..."
cd ../frontend
npm install

# 构建前端
echo "🔨 构建前端..."
npm run build

# 启动服务
echo "🚀 启动服务..."
cd ..
pm2 restart game-boost-backend || pm2 start ecosystem.config.js

echo "✅ 部署完成！"
```

### 数据库初始化脚本
创建 `init-db.sh` 文件：
```bash
#!/bin/bash
echo "🗄️ 初始化数据库..."

cd /www/wwwroot/game-boost/backend

# 生成 Prisma 客户端
npx prisma generate

# 运行数据库迁移
npx prisma migrate deploy

# 初始化种子数据
npm run seed

echo "✅ 数据库初始化完成！"
```

## 📱 移动端适配

### 响应式配置
在 Nginx 配置中添加移动端优化：
```nginx
# 移动端检测
set $mobile_rewrite do_not_perform;
if ($http_user_agent ~* "(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino") {
    set $mobile_rewrite perform;
}

# 移动端缓存策略
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Vary Accept-Encoding;
}
```

## 🔧 性能优化

### 1. 启用 Gzip 压缩
```nginx
# 在 server 块中添加
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
```

### 2. 配置缓存
```nginx
# 静态资源缓存
location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# HTML 文件不缓存
location ~* \.html$ {
    add_header Cache-Control "no-cache, no-store, must-revalidate";
}
```

### 3. PM2 集群模式
修改 `ecosystem.config.js`：
```javascript
module.exports = {
  apps: [{
    name: 'game-boost-backend',
    script: './backend/dist/index.js',
    instances: 'max', // 使用所有CPU核心
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
}
```

## 🔍 监控和日志

### 1. 安装监控工具
```bash
# 安装 htop
yum install htop -y  # CentOS
apt install htop -y  # Ubuntu

# 安装 iotop
yum install iotop -y  # CentOS
apt install iotop -y  # Ubuntu
```

### 2. 日志轮转配置
创建 `/etc/logrotate.d/game-boost`：
```
/www/wwwroot/game-boost/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www www
    postrotate
        pm2 reload game-boost-backend
    endscript
}
```

### 3. 系统监控脚本
创建 `monitor.sh`：
```bash
#!/bin/bash
# 系统监控脚本

echo "=== 系统状态监控 ==="
echo "时间: $(date)"
echo ""

echo "=== CPU 使用率 ==="
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}'

echo "=== 内存使用情况 ==="
free -h

echo "=== 磁盘使用情况 ==="
df -h

echo "=== PM2 进程状态 ==="
pm2 list

echo "=== 最近的错误日志 ==="
tail -n 10 /www/wwwroot/game-boost/logs/err.log
```

## 🛡️ 安全加固

### 1. 隐藏 Nginx 版本
在 `/etc/nginx/nginx.conf` 中添加：
```nginx
http {
    server_tokens off;
}
```

### 2. 限制请求频率
```nginx
# 在 http 块中添加
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;

# 在 location /api/ 中添加
limit_req zone=api burst=20 nodelay;
```

### 3. 禁用不必要的 HTTP 方法
```nginx
# 在 server 块中添加
if ($request_method !~ ^(GET|HEAD|POST|PUT|DELETE)$ ) {
    return 405;
}
```

## 📋 维护检查清单

### 每日检查
- [ ] 检查服务器资源使用情况
- [ ] 查看应用日志是否有错误
- [ ] 确认备份是否正常执行
- [ ] 检查 PM2 进程状态

### 每周检查
- [ ] 更新系统安全补丁
- [ ] 清理旧的日志文件
- [ ] 检查磁盘空间使用情况
- [ ] 验证 SSL 证书有效期

### 每月检查
- [ ] 更新 Node.js 和依赖包
- [ ] 检查数据库性能
- [ ] 审查安全日志
- [ ] 测试备份恢复流程

恭喜！您的游戏代练管理系统已成功部署到宝塔面板！🎉
