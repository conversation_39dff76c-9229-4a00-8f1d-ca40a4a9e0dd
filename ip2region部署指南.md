# 🌍 ip2region 离线IP地址库部署指南

## 📋 概述

ip2region是王者荣耀代练管理系统中用于离线IP地理位置查询的核心组件。它提供高性能的本地IP地址解析功能，无需联网即可获取IP的地理位置信息。

## 🎯 功能特点

### 核心优势
- 🌍 **离线查询**: 无需网络连接，本地数据库查询
- 🚀 **高性能**: 内存缓存模式，单次查询10-50微秒
- 📊 **详细信息**: 包含国家、省份、城市、ISP信息
- 💾 **小体积**: 数据库文件约11MB，包含全球IP段
- 🔒 **隐私保护**: 本地查询，不泄露用户IP信息

### 应用场景
- 👥 **用户登录地理统计**: 分析用户登录地域分布
- 🔍 **在线用户监控**: 实时显示在线用户地理位置
- 📈 **业务数据分析**: 基于地理位置的业务分析
- 🛡️ **安全监控**: 异常登录地理位置检测

## 📁 文件结构

```
/www/wwwroot/game-boost/
├── ip2region-master/                    # ip2region源文件目录
│   ├── data/
│   │   └── ip2region.xdb               # 原始数据库文件 (~11MB)
│   └── binding/
│       └── nodejs/
│           ├── index.js                # Node.js绑定库
│           └── package.json            # 包配置文件
├── backend/
│   └── src/
│       ├── data/
│       │   └── ip2region.xdb          # 部署的数据库文件
│       ├── lib/
│       │   ├── index.js               # 部署的绑定库
│       │   └── package.json           # 包配置文件
│       └── services/
│           └── ipLocationService.ts    # IP地理位置服务
```

## 🚀 快速部署

### 方式一：自动配置 (推荐)
```bash
# 运行项目配置脚本 (已包含ip2region配置)
bash scripts/bt-project-config.sh

# 或单独配置ip2region
bash scripts/setup-ip2region.sh
```

### 方式二：手动配置
```bash
# 1. 创建目标目录
mkdir -p /www/wwwroot/game-boost/backend/src/data
mkdir -p /www/wwwroot/game-boost/backend/src/lib

# 2. 复制数据库文件
cp /www/wwwroot/game-boost/ip2region-master/data/ip2region.xdb \
   /www/wwwroot/game-boost/backend/src/data/

# 3. 复制绑定库
cp /www/wwwroot/game-boost/ip2region-master/binding/nodejs/index.js \
   /www/wwwroot/game-boost/backend/src/lib/
cp /www/wwwroot/game-boost/ip2region-master/binding/nodejs/package.json \
   /www/wwwroot/game-boost/backend/src/lib/

# 4. 设置权限
chmod 644 /www/wwwroot/game-boost/backend/src/data/ip2region.xdb
chmod 644 /www/wwwroot/game-boost/backend/src/lib/*
chown -R www:www /www/wwwroot/game-boost/backend/src/data/
chown -R www:www /www/wwwroot/game-boost/backend/src/lib/
```

## 🔍 验证部署

### 一键验证
```bash
# 运行完整验证脚本
bash scripts/verify-ip2region.sh

# 检查状态
bash scripts/check-ip2region-status.sh
```

### 手动验证
```bash
# 1. 检查文件存在
ls -la /www/wwwroot/game-boost/backend/src/data/ip2region.xdb
ls -la /www/wwwroot/game-boost/backend/src/lib/index.js

# 2. 检查文件大小 (应该约11MB)
stat /www/wwwroot/game-boost/backend/src/data/ip2region.xdb

# 3. 功能测试
cd /www/wwwroot/game-boost
node -e "
const ip2region = require('./backend/src/lib/index.js');
const path = require('path');
const dbPath = path.join(__dirname, 'backend/src/data/ip2region.xdb');
const buffer = ip2region.loadContentFromFile(dbPath);
const searcher = ip2region.newWithBuffer(buffer);
searcher.search('*******').then(result => {
  console.log('测试结果:', result.region);
  console.log('查询耗时:', result.took, '微秒');
}).catch(err => console.error('测试失败:', err));
"
```

## 💻 代码使用示例

### 基础使用
```typescript
import { IpLocationService } from '../services/ipLocationService';

const ipLocationService = new IpLocationService();

// 查询单个IP
const location = await ipLocationService.getLocation('*******');
console.log(location);
// 输出: {
//   country: '美国',
//   region: '北美洲',
//   province: '加利福尼亚',
//   city: '山景城',
//   isp: 'Google',
//   fullLocation: '美国|北美洲|加利福尼亚|山景城|Google'
// }

// 批量查询
const ips = ['*******', '***************', '*********'];
const locations = await ipLocationService.getLocationsBatch(ips);
```

### 在路由中使用
```typescript
// 获取用户IP地理位置
app.post('/api/login', async (req, res) => {
  const userIP = req.ip || req.connection.remoteAddress;
  const location = await ipLocationService.getLocation(userIP);
  
  // 记录登录日志
  await loginLogService.create({
    userId: user.id,
    ipAddress: userIP,
    location: location.fullLocation,
    city: location.city,
    province: location.province
  });
  
  res.json({ success: true, location });
});
```

## ⚡ 性能优化

### 内存缓存模式 (推荐)
```typescript
// 使用内存缓存模式，性能最佳
const buffer = ip2region.loadContentFromFile(dbPath);
const searcher = ip2region.newWithBuffer(buffer);
```

### 性能指标
- **查询速度**: 10-50微秒/次
- **内存占用**: ~15MB (缓存模式)
- **并发支持**: 高并发查询
- **准确率**: >99%

## 🔧 维护管理

### 状态监控
```bash
# 检查ip2region状态
bash scripts/check-ip2region-status.sh

# 查看相关日志
grep -i "ip.*location" /www/wwwroot/game-boost/logs/combined.log

# 监控查询性能
grep "ip地址解析成功" /www/wwwroot/game-boost/logs/combined.log | tail -10
```

### 数据库更新
```bash
# 备份当前数据库
cp /www/wwwroot/game-boost/backend/src/data/ip2region.xdb \
   /www/wwwroot/game-boost/backend/src/data/ip2region.xdb.backup

# 更新数据库文件 (如有新版本)
# 下载新版本后替换文件，然后重启服务
pm2 restart game-boost-backend
```

## 🚨 故障排除

### 常见问题

1. **数据库文件不存在**
   ```bash
   # 重新配置
   bash scripts/setup-ip2region.sh
   ```

2. **查询返回"未知"**
   ```bash
   # 检查文件完整性
   ls -lh /www/wwwroot/game-boost/backend/src/data/ip2region.xdb
   # 重新复制数据库文件
   ```

3. **性能问题**
   ```bash
   # 确认使用内存缓存模式
   # 检查PM2内存限制配置
   ```

### 诊断工具
```bash
# 收集诊断信息
bash scripts/collect-ip2region-info.sh

# 完整验证
bash scripts/verify-ip2region.sh
```

## 📊 监控指标

### 关键指标
- **数据库文件大小**: ~11MB
- **查询响应时间**: <50微秒
- **内存使用**: ~15MB
- **查询成功率**: >99%
- **文件权限**: 644 (www:www)

### 告警阈值
- 查询耗时 >100微秒
- 查询失败率 >1%
- 数据库文件大小 <10MB
- 内存使用 >50MB

## 🔗 相关文档

- [ip2region故障排除指南](docs/ip2region故障排除指南.md)
- [宝塔部署完整指南](宝塔部署完整指南.md)
- [项目技术栈规范](技术栈规范.md)

## 📞 技术支持

### 获取帮助
1. 查看故障排除指南
2. 运行诊断脚本
3. 检查系统日志
4. 联系技术支持

### 常用命令速查
```bash
# 配置ip2region
bash scripts/setup-ip2region.sh

# 验证部署
bash scripts/verify-ip2region.sh

# 检查状态
bash scripts/check-ip2region-status.sh

# 查看日志
tail -f /www/wwwroot/game-boost/logs/combined.log | grep -i ip
```

---

**记住**: ip2region是离线IP库，确保数据库文件完整是关键！
