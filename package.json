{"name": "ace-platform-system", "version": "1.0.0", "description": "Ace Platform 任务分发管理系统", "scripts": {"copyright:setup": "node scripts/setup-auth.js", "copyright:add": "node scripts/add-copyright.js", "copyright:remove": "node scripts/remove-copyright.js", "copyright:demo": "node scripts/demo-copyright.js", "copyright:restore": "node scripts/demo-copyright.js restore", "copyright:cleanup": "node scripts/cleanup-backups.js", "copyright:help": "type scripts\\README.md", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build"}, "keywords": ["ace-platform", "task-management", "copyright-tool"], "author": "Ace Platform Team", "license": "PROPRIETARY", "dependencies": {"axios": "^1.10.0"}}