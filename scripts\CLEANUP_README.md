# 备份文件清理工具

这个工具集用于清理项目中残留的备份文件，包括以下格式的文件：
- `*.backup-footer-*`
- `*.backup-standalone-*`
- `*.backup-fix-*`
- `*.backup-*`
- `*.bak`
- `*.old`

## 🚀 快速使用

### Windows 用户（推荐）

**方法1: 使用批处理文件（最简单）**
```bash
# 查看要删除的文件（安全模式）
scripts\cleanup-backup-files.bat --dry-run

# 直接删除所有备份文件
scripts\cleanup-backup-files.bat --force
```

**方法2: 使用 PowerShell（功能更强）**
```powershell
# 查看要删除的文件
.\scripts\cleanup-backup-files.ps1 -DryRun

# 直接删除所有备份文件
.\scripts\cleanup-backup-files.ps1 -Force

# 显示详细过程
.\scripts\cleanup-backup-files.ps1 -Verbose
```

### Node.js 用户

```bash
# 查看要删除的文件
node scripts/cleanup-backup-files.js --dry-run

# 直接删除所有备份文件
node scripts/cleanup-backup-files.js --force

# 显示详细过程
node scripts/cleanup-backup-files.js --verbose
```

## 📋 命令选项

| 选项 | 批处理 | PowerShell | Node.js | 说明 |
|------|--------|------------|---------|------|
| 干运行 | `--dry-run`, `-d` | `-DryRun` | `--dry-run`, `-d` | 只显示要删除的文件，不实际删除 |
| 强制删除 | `--force`, `-f` | `-Force` | `--force`, `-f` | 不询问确认，直接删除 |
| 详细信息 | `--verbose`, `-v` | `-Verbose` | `--verbose`, `-v` | 显示详细的执行过程 |
| 帮助 | `--help`, `-h` | `-Help` | `--help`, `-h` | 显示帮助信息 |

## 🎯 扫描目录

脚本会扫描以下目录：
- `frontend/src/views`
- `frontend/src/components`
- `frontend/src/stores`
- `frontend/src/api`
- `frontend/src/utils`
- `backend/src`

## 🔍 识别的备份文件模式

- **Footer备份**: `*.backup-footer-1753837511423`
- **Standalone备份**: `*.backup-standalone-1753838073882`
- **Fix备份**: `*.backup-fix-1753837928541`
- **通用备份**: `*.backup-*`
- **传统备份**: `*.bak`, `*.old`

## 📊 输出示例

```
🧹 备份文件清理工具

ℹ️ 开始扫描备份文件...

📋 发现 15 个备份文件:
  1. frontend\src\views\boss\Orders.vue.backup-footer-1753837511436 (12.5 KB, 2024-01-29 15:25:11)
  2. frontend\src\views\boss\Tasks.vue.backup-footer-1753837511442 (8.3 KB, 2024-01-29 15:25:11)
  3. frontend\src\views\Profile.vue.backup-standalone-1753838073882 (5.2 KB, 2024-01-29 15:41:13)
  ...

📊 总计: 15 个文件, 156.7 KB

⚠️ 确定要删除这些备份文件吗？(y/N): y

ℹ️ 开始删除备份文件...
✅ 成功删除 15 个备份文件
✅ 清理完成！
```

## ⚠️ 安全提示

1. **建议先使用干运行模式**查看要删除的文件：
   ```bash
   scripts\cleanup-backup-files.bat --dry-run
   ```

2. **确认文件列表**后再执行实际删除：
   ```bash
   scripts\cleanup-backup-files.bat --force
   ```

3. **重要文件请手动检查**，确保不会误删重要的备份文件

## 🛠️ 故障排除

### 权限问题
如果遇到权限问题，请以管理员身份运行：
- 批处理：右键 → "以管理员身份运行"
- PowerShell：以管理员身份打开 PowerShell

### PowerShell 执行策略
如果 PowerShell 脚本无法执行，请设置执行策略：
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 文件被占用
如果某些文件正在被使用，请：
1. 关闭相关的编辑器或IDE
2. 重新运行脚本

## 📝 日志记录

- **批处理版本**：输出到控制台
- **PowerShell版本**：支持详细模式，显示更多信息
- **Node.js版本**：支持彩色输出和详细日志

## 🔄 定期清理建议

建议定期运行此脚本来保持项目目录的整洁：
- 每周运行一次干运行模式检查
- 每月执行一次完整清理
- 在重要提交前清理备份文件
