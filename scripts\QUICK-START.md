# 🚀 Ace Platform 版权工具快速使用指南

## 📋 工具概述

这是一套专为你的 Ace Platform 系统设计的版权管理工具，具有强大的安全认证机制，确保只有你可以使用。

## ⚡ 快速开始

### 方式一：使用 npm 脚本（推荐）

```bash
# 1. 首次设置认证密钥
npm run copyright:setup

# 2. 演示版权添加（安全测试）
npm run copyright:demo

# 3. 恢复演示文件
npm run copyright:restore

# 4. 正式添加版权（需要认证）
npm run copyright:add

# 5. 删除版权信息（需要认证）
npm run copyright:remove

# 6. 清理备份文件
npm run copyright:cleanup
```

### 方式二：直接运行脚本

```bash
# 1. 设置认证密钥
node scripts/setup-auth.js

# 2. 演示版权添加
node scripts/demo-copyright.js

# 3. 正式添加版权
node scripts/add-copyright.js

# 4. 删除版权信息
node scripts/remove-copyright.js
```

### 方式三：使用批处理文件（Windows）

```bash
# 运行图形化界面
scripts/copyright-tool.bat
```

## 🔐 安全认证流程

### 1. 首次设置密钥
```bash
npm run copyright:setup
```
- 设置一个至少8位的安全密钥
- 密钥会被 SHA256 加密存储
- 只有你知道这个密钥

### 2. 使用认证密钥
```bash
npm run copyright:add
```
- 输入之前设置的密钥
- 认证成功后开始处理文件
- 失败3次会锁定5分钟

## 🎯 功能特性

### ✅ 智能处理
- 自动检测已有版权信息，避免重复添加
- 支持多种文件类型：JS/TS/Vue/CSS/HTML
- 自动排除不需要的目录：node_modules、.git 等

### 🔒 安全保护
- SHA256 密钥加密
- 失败重试限制
- 自动锁定机制

### 📊 详细统计
- 处理文件数量统计
- 跳过文件原因说明
- 失败文件错误详情

## 📁 支持的文件类型

| 文件类型 | 扩展名 | 版权格式 |
|---------|--------|----------|
| JavaScript/TypeScript | `.js` `.ts` `.jsx` `.tsx` | `/** */` 注释 |
| Vue 组件 | `.vue` | `<!-- -->` 注释 |
| 样式文件 | `.css` `.scss` `.sass` `.less` | `/* */` 注释 |
| HTML 文件 | `.html` `.htm` | `<!-- -->` 注释 |

## 🚫 自动排除规则

- `node_modules/` - 依赖包目录
- `.git/` - Git 版本控制
- `dist/` `build/` - 构建输出
- `*.log` - 日志文件
- `.env*` - 环境变量文件
- 锁定文件：`package-lock.json` `yarn.lock`

## 💡 使用建议

### 🧪 先测试后使用
```bash
# 1. 先运行演示模式测试
npm run copyright:demo

# 2. 检查效果后恢复
npm run copyright:restore

# 3. 确认无误后正式运行
npm run copyright:add
```

### 📋 版权信息示例

添加的版权信息格式：
```javascript
/**
 * Copyright (c) 2025 Ace Platform系统
 * 
 * 本软件受版权保护，未经授权不得复制、修改或分发
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 * @created 2025-07-29
 */
```

## 🛠️ 自定义配置

### 修改版权信息
编辑 `scripts/add-copyright.js` 中的 `COPYRIGHT_TEMPLATES` 对象

### 添加新文件类型
在 `FILE_TYPE_MAP` 中添加新的扩展名映射

### 修改排除规则
在 `EXCLUDE_PATTERNS` 数组中调整排除模式

## ⚠️ 重要提醒

1. **密钥安全**: 请妥善保管认证密钥，遗失后需重新设置
2. **备份建议**: 重要项目建议先备份再运行
3. **测试优先**: 建议先用演示模式测试效果
4. **权限检查**: 确保对文件有写入权限

## 🆘 常见问题

### Q: 忘记认证密钥怎么办？
A: 重新运行 `npm run copyright:setup` 设置新密钥

### Q: 认证被锁定怎么办？
A: 等待5分钟后重试，或重新设置密钥

### Q: 如何批量移除版权信息？
A: 目前需要手动处理，或使用 Git 恢复到之前版本

### Q: 可以自定义版权内容吗？
A: 可以，编辑脚本中的模板即可

## 📞 技术支持

如有问题，请查看详细文档：`scripts/README.md`

---

**🎉 现在你就可以安全地为所有代码添加版权保护了！**
