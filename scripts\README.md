# Ace Platform 版权管理工具

## 📋 功能概述

这是一套专为 Ace Platform 系统设计的版权管理工具，具有以下特性：

- 🔐 **安全认证**: 只有通过认证的用户才能使用
- 🎯 **智能检测**: 自动检测已有版权信息，避免重复添加
- 📁 **多文件支持**: 支持 JS/TS/Vue/CSS/HTML 等多种文件类型
- 🚫 **智能排除**: 自动排除 node_modules、.git 等不需要的目录
- 📊 **详细统计**: 提供完整的处理结果统计

## 🚀 快速开始

### 1. 首次设置认证密钥

```bash
# 设置认证密钥（只需要执行一次）
node scripts/setup-auth.js
```

按照提示设置一个安全的认证密钥（至少8位字符）。

### 2. 运行版权添加脚本

```bash
# 为所有代码文件添加版权信息
node scripts/add-copyright.js
```

输入之前设置的认证密钥即可开始处理。

## 📁 文件结构

```
scripts/
├── add-copyright.js     # 主版权添加脚本
├── setup-auth.js       # 认证密钥设置工具
├── auth-config.json    # 认证配置文件（自动生成）
└── README.md           # 使用说明文档
```

## 🔧 支持的文件类型

| 文件类型 | 扩展名 | 版权格式 |
|---------|--------|----------|
| JavaScript/TypeScript | .js, .ts, .jsx, .tsx | `/** */` 注释 |
| Vue 组件 | .vue | `<!-- -->` 注释 |
| 样式文件 | .css, .scss, .sass, .less | `/* */` 注释 |
| HTML 文件 | .html, .htm | `<!-- -->` 注释 |

## 🚫 自动排除的目录/文件

- `node_modules/` - 依赖包目录
- `.git/` - Git 版本控制目录
- `dist/`, `build/` - 构建输出目录
- `*.log` - 日志文件
- `.env*` - 环境变量文件
- `package-lock.json`, `yarn.lock` - 锁定文件

## 🔐 安全特性

### 认证机制
- 使用 SHA256 哈希算法保护密钥
- 支持失败重试限制（最多3次）
- 失败后自动锁定5分钟

### 版权保护
- 智能检测已有版权信息
- 避免重复添加版权声明
- 支持自定义版权模板

## 📊 版权信息模板

### JavaScript/TypeScript 文件
```javascript
/**
 * Copyright (c) 2025 Ace Platform系统
 * 
 * 本软件受版权保护，未经授权不得复制、修改或分发
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 * @created 2025-07-29
 */
```

### Vue 组件文件
```vue
<!--
  Copyright (c) 2025 Ace Platform系统
  
  本软件受版权保护，未经授权不得复制、修改或分发
  
  <AUTHOR> Platform Team
  @version 1.0.0
  @created 2025-07-29
-->
```

### CSS 样式文件
```css
/*
 * Copyright (c) 2025 Ace Platform系统
 * 
 * 本软件受版权保护，未经授权不得复制、修改或分发
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 * @created 2025-07-29
 */
```

## 🛠️ 高级配置

### 修改版权信息
编辑 `add-copyright.js` 文件中的 `COPYRIGHT_TEMPLATES` 对象来自定义版权信息。

### 添加新的文件类型支持
在 `FILE_TYPE_MAP` 对象中添加新的文件扩展名映射。

### 修改排除规则
在 `EXCLUDE_PATTERNS` 数组中添加或修改排除模式。

## 🔄 使用流程

1. **首次使用**: 运行 `setup-auth.js` 设置认证密钥
2. **日常使用**: 运行 `add-copyright.js` 添加版权信息
3. **安全保护**: 脚本会要求输入认证密钥
4. **智能处理**: 自动扫描、检测、添加版权信息
5. **结果统计**: 显示处理结果和统计信息

## ⚠️ 注意事项

- 请妥善保管认证密钥，遗失后需要重新设置
- 建议在添加版权前备份重要文件
- 脚本会自动跳过已有版权信息的文件
- 如需修改版权内容，请编辑模板后重新运行

## 🆘 故障排除

### 认证失败
- 检查密钥是否正确
- 等待锁定时间结束后重试
- 重新运行 `setup-auth.js` 设置新密钥

### 文件处理失败
- 检查文件权限
- 确保文件未被其他程序占用
- 查看错误信息进行针对性处理

## 📞 技术支持

如有问题，请联系 Ace Platform 技术团队。
