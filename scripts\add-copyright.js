#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const readline = require('readline');

// 认证配置
const AUTH_CONFIG = {
  // 你的认证密钥 (SHA256哈希值)
  MASTER_KEY_HASH: '0ef4f22d318a6b05072f06bc3dcff84a982b3fafbeb6d3a35134ebb6eb16519d', // 'hello123' 的哈希值，请修改
  MAX_ATTEMPTS: 3,
  LOCKOUT_TIME: 300000 // 5分钟锁定时间
};

// 版权信息模板
const COPYRIGHT_TEMPLATES = {
  js: `/**
 * Copyright (c) ${new Date().getFullYear()} Ace Platform系统
 * 
 * 本软件受版权保护，未经授权不得复制、修改或分发
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 * @created ${new Date().toISOString().split('T')[0]}
 */

`,
  
  vue: `<!--
  Copyright (c) ${new Date().getFullYear()} Ace Platform系统
  
  本软件受版权保护，未经授权不得复制、修改或分发
  
  <AUTHOR> Platform Team
  @version 1.0.0
  @created ${new Date().toISOString().split('T')[0]}
-->

`,

  css: `/*
 * Copyright (c) ${new Date().getFullYear()} Ace Platform系统
 * 
 * 本软件受版权保护，未经授权不得复制、修改或分发
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 * @created ${new Date().toISOString().split('T')[0]}
 */

`,

  html: `<!--
  Copyright (c) ${new Date().getFullYear()} Ace Platform系统
  
  本软件受版权保护，未经授权不得复制、修改或分发
  
  <AUTHOR> Platform Team
  @version 1.0.0
  @created ${new Date().toISOString().split('T')[0]}
-->

`
};

// 文件类型映射
const FILE_TYPE_MAP = {
  '.js': 'js',
  '.ts': 'js',
  '.jsx': 'js',
  '.tsx': 'js',
  '.vue': 'vue',
  '.css': 'css',
  '.scss': 'css',
  '.sass': 'css',
  '.less': 'css',
  '.html': 'html',
  '.htm': 'html'
};

// 需要排除的目录和文件
const EXCLUDE_PATTERNS = [
  'node_modules',
  '.git',
  'dist',
  'build',
  '.next',
  '.nuxt',
  'coverage',
  '.nyc_output',
  'logs',
  '*.log',
  '.env*',
  'package-lock.json',
  'yarn.lock',
  'pnpm-lock.yaml'
];

class CopyrightManager {
  constructor() {
    this.failedAttempts = 0;
    this.lastAttemptTime = 0;
    this.isLocked = false;
  }

  // 认证功能
  async authenticate() {
    if (this.isLocked) {
      const timeLeft = Math.ceil((AUTH_CONFIG.LOCKOUT_TIME - (Date.now() - this.lastAttemptTime)) / 1000);
      console.log(`🔒 认证已锁定，请等待 ${timeLeft} 秒后重试`);
      return false;
    }

    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    return new Promise((resolve) => {
      rl.question('🔐 请输入认证密钥: ', (input) => {
        rl.close();
        
        const inputHash = crypto.createHash('sha256').update(input.trim()).digest('hex');
        
        if (inputHash === AUTH_CONFIG.MASTER_KEY_HASH) {
          console.log('✅ 认证成功！');
          this.failedAttempts = 0;
          resolve(true);
        } else {
          this.failedAttempts++;
          this.lastAttemptTime = Date.now();
          
          console.log(`❌ 认证失败！剩余尝试次数: ${AUTH_CONFIG.MAX_ATTEMPTS - this.failedAttempts}`);
          
          if (this.failedAttempts >= AUTH_CONFIG.MAX_ATTEMPTS) {
            this.isLocked = true;
            console.log(`🔒 认证失败次数过多，已锁定 ${AUTH_CONFIG.LOCKOUT_TIME / 1000} 秒`);
          }
          
          resolve(false);
        }
      });
    });
  }

  // 检查文件是否已有版权信息
  hasExistingCopyright(content) {
    const copyrightPatterns = [
      /Copyright.*Ace Platform/i,
      /版权.*Ace Platform/i,
      /@author.*Ace Platform/i,
      /本软件受版权保护/i
    ];
    
    return copyrightPatterns.some(pattern => pattern.test(content));
  }

  // 获取文件的版权模板
  getCopyrightTemplate(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const templateType = FILE_TYPE_MAP[ext];
    
    if (!templateType) {
      return null;
    }
    
    return COPYRIGHT_TEMPLATES[templateType];
  }

  // 检查文件是否应该被排除
  shouldExcludeFile(filePath) {
    const relativePath = path.relative(process.cwd(), filePath);
    
    return EXCLUDE_PATTERNS.some(pattern => {
      if (pattern.includes('*')) {
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        return regex.test(relativePath);
      }
      return relativePath.includes(pattern);
    });
  }

  // 递归获取所有文件
  getAllFiles(dirPath, fileList = []) {
    const files = fs.readdirSync(dirPath);
    
    files.forEach(file => {
      const filePath = path.join(dirPath, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        if (!this.shouldExcludeFile(filePath)) {
          this.getAllFiles(filePath, fileList);
        }
      } else {
        if (!this.shouldExcludeFile(filePath)) {
          fileList.push(filePath);
        }
      }
    });
    
    return fileList;
  }

  // 为单个文件添加版权信息
  addCopyrightToFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 检查是否已有版权信息
      if (this.hasExistingCopyright(content)) {
        console.log(`⏭️  跳过 ${filePath} (已有版权信息)`);
        return { success: true, skipped: true };
      }
      
      // 获取版权模板
      const template = this.getCopyrightTemplate(filePath);
      if (!template) {
        console.log(`⏭️  跳过 ${filePath} (不支持的文件类型)`);
        return { success: true, skipped: true };
      }
      
      // 添加版权信息
      const newContent = template + content;
      fs.writeFileSync(filePath, newContent, 'utf8');
      
      console.log(`✅ 已添加版权信息: ${filePath}`);
      return { success: true, skipped: false };
      
    } catch (error) {
      console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
      return { success: false, error: error.message };
    }
  }

  // 主执行函数
  async run() {
    console.log('🚀 Ace Platform 版权添加工具启动');
    console.log('=====================================');
    
    // 认证检查
    const isAuthenticated = await this.authenticate();
    if (!isAuthenticated) {
      console.log('❌ 认证失败，程序退出');
      process.exit(1);
    }
    
    console.log('\n📁 开始扫描文件...');
    
    // 获取所有文件
    const allFiles = this.getAllFiles(process.cwd());
    const supportedFiles = allFiles.filter(file => this.getCopyrightTemplate(file));
    
    console.log(`📊 扫描完成: 找到 ${supportedFiles.length} 个支持的文件`);
    
    if (supportedFiles.length === 0) {
      console.log('ℹ️  没有找到需要处理的文件');
      return;
    }
    
    // 处理文件
    console.log('\n🔄 开始添加版权信息...');
    
    let processed = 0;
    let skipped = 0;
    let failed = 0;
    
    for (const file of supportedFiles) {
      const result = this.addCopyrightToFile(file);
      
      if (result.success) {
        if (result.skipped) {
          skipped++;
        } else {
          processed++;
        }
      } else {
        failed++;
      }
    }
    
    // 输出统计结果
    console.log('\n📈 处理完成统计:');
    console.log(`✅ 成功添加: ${processed} 个文件`);
    console.log(`⏭️  跳过文件: ${skipped} 个文件`);
    console.log(`❌ 失败文件: ${failed} 个文件`);
    console.log(`📁 总计文件: ${supportedFiles.length} 个文件`);
    
    console.log('\n🎉 版权添加任务完成！');
  }
}

// 主程序入口
if (require.main === module) {
  const manager = new CopyrightManager();
  manager.run().catch(error => {
    console.error('💥 程序执行出错:', error);
    process.exit(1);
  });
}

module.exports = CopyrightManager;
