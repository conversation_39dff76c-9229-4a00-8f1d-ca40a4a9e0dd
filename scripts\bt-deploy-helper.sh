#!/bin/bash

# 🚀 宝塔面板部署助手脚本
# 游戏代练管理系统自动化部署工具

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
PROJECT_NAME="game-boost"
PROJECT_DIR="/www/wwwroot/${PROJECT_NAME}"
DB_NAME="game_boost_db"
DB_USER="game_boost_user"
DOMAIN=""
DB_PASSWORD=""
JWT_SECRET=""
REDIS_PASSWORD=""
NODE_VERSION="18"

# 显示欢迎信息
show_welcome() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "🚀 游戏代练管理系统 - 宝塔面板部署助手"
    echo "=================================================="
    echo -e "${NC}"
    echo "本脚本将帮助您在宝塔面板上部署游戏代练管理系统"
    echo ""
}

# 检查运行环境
check_environment() {
    log_info "检查运行环境..."

    # 检查是否为 root 用户
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用 root 用户运行此脚本"
        exit 1
    fi

    # 检查宝塔面板是否安装
    if [ ! -f "/www/server/panel/BT-Panel" ]; then
        log_error "未检测到宝塔面板，请先安装宝塔面板"
        exit 1
    fi

    # 检查必要的命令
    local missing_commands=()
    for cmd in node npm nginx mysql pm2; do
        if ! command -v $cmd &> /dev/null; then
            missing_commands+=($cmd)
            log_warning "$cmd 未安装"
        else
            log_info "$cmd 已安装: $(command -v $cmd)"
        fi
    done

    # 如果有缺失的命令，提供安装建议
    if [ ${#missing_commands[@]} -gt 0 ]; then
        log_warning "缺失的软件: ${missing_commands[*]}"
        log_info "请在宝塔面板软件商店安装以下软件:"
        for cmd in "${missing_commands[@]}"; do
            case $cmd in
                node|npm) echo "  - Node.js版本管理器 (选择Node.js $NODE_VERSION.x)" ;;
                nginx) echo "  - Nginx 1.18+" ;;
                mysql) echo "  - MySQL 8.0+" ;;
                pm2) echo "  - PM2管理器" ;;
            esac
        done
        read -p "是否继续部署? (y/N): " continue_deploy
        if [[ ! $continue_deploy =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi

    log_success "环境检查完成"
}

# 收集用户输入
collect_user_input() {
    log_info "收集部署配置信息..."
    
    # 域名
    while [ -z "$DOMAIN" ]; do
        read -p "请输入您的域名 (例如: example.com): " DOMAIN
        if [ -z "$DOMAIN" ]; then
            log_warning "域名不能为空"
        fi
    done
    
    # 数据库密码
    while [ -z "$DB_PASSWORD" ]; do
        read -s -p "请输入数据库密码: " DB_PASSWORD
        echo
        if [ -z "$DB_PASSWORD" ]; then
            log_warning "数据库密码不能为空"
        fi
    done
    
    # JWT 密钥
    if [ -z "$JWT_SECRET" ]; then
        JWT_SECRET=$(openssl rand -base64 32)
        log_info "自动生成 JWT 密钥: $JWT_SECRET"
    fi
    
    log_success "配置信息收集完成"
}

# 创建项目目录
create_project_directory() {
    log_info "创建项目目录..."
    
    if [ ! -d "$PROJECT_DIR" ]; then
        mkdir -p "$PROJECT_DIR"
        chown -R www:www "$PROJECT_DIR"
        log_success "项目目录创建完成: $PROJECT_DIR"
    else
        log_warning "项目目录已存在: $PROJECT_DIR"
    fi
}

# 配置数据库
setup_database() {
    log_info "配置数据库..."
    
    # 创建数据库
    mysql -e "CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>/dev/null || {
        log_error "创建数据库失败，请检查 MySQL 服务状态"
        exit 1
    }
    
    # 创建用户并授权
    mysql -e "CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASSWORD';" 2>/dev/null
    mysql -e "GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';" 2>/dev/null
    mysql -e "FLUSH PRIVILEGES;" 2>/dev/null
    
    log_success "数据库配置完成"
}

# 部署后端
deploy_backend() {
    log_info "部署后端服务..."
    
    cd "$PROJECT_DIR/backend"
    
    # 安装依赖
    log_info "安装后端依赖..."
    npm install --production
    
    # 创建环境配置文件
    log_info "创建环境配置文件..."
    cat > .env << EOF
# 数据库配置
DATABASE_URL="mysql://$DB_USER:$DB_PASSWORD@localhost:3306/$DB_NAME"

# JWT 密钥
JWT_SECRET="$JWT_SECRET"

# 服务器配置
PORT=3000
NODE_ENV=production

# 文件上传配置
UPLOAD_PATH="$PROJECT_DIR/uploads"
MAX_FILE_SIZE=10485760

# 前端地址
FRONTEND_URL="https://$DOMAIN"
EOF
    
    # 生成 Prisma 客户端
    log_info "生成 Prisma 客户端..."
    npx prisma generate
    
    # 运行数据库迁移
    log_info "运行数据库迁移..."
    npx prisma migrate deploy
    
    # 构建后端
    log_info "构建后端..."
    npm run build
    
    log_success "后端部署完成"
}

# 部署前端
deploy_frontend() {
    log_info "部署前端..."
    
    cd "$PROJECT_DIR/frontend"
    
    # 安装依赖
    log_info "安装前端依赖..."
    npm install
    
    # 创建生产环境配置
    log_info "创建前端环境配置..."
    cat > .env.production << EOF
# API 基础地址
VITE_API_BASE_URL=https://$DOMAIN/api
VITE_APP_TITLE=游戏代练管理系统

# 文件上传配置
VITE_UPLOAD_URL=https://$DOMAIN/api/upload
EOF
    
    # 构建前端
    log_info "构建前端..."
    npm run build
    
    log_success "前端部署完成"
}

# 配置 PM2
setup_pm2() {
    log_info "配置 PM2 进程管理..."
    
    cd "$PROJECT_DIR"
    
    # 创建 PM2 配置文件
    cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'game-boost-backend',
    script: './backend/dist/index.js',
    cwd: '$PROJECT_DIR',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    restart_delay: 4000
  }]
}
EOF
    
    # 创建日志目录
    mkdir -p logs
    chown -R www:www logs
    
    # 启动服务
    pm2 start ecosystem.config.js
    pm2 save
    
    log_success "PM2 配置完成"
}

# 配置 Nginx
setup_nginx() {
    log_info "配置 Nginx..."
    
    # 创建 Nginx 配置文件
    cat > "/www/server/panel/vhost/nginx/$DOMAIN.conf" << EOF
server {
    listen 80;
    listen 443 ssl http2;
    server_name $DOMAIN;
    
    root $PROJECT_DIR/frontend/dist;
    index index.html;
    
    # SSL 配置（需要在宝塔面板中配置证书）
    # ssl_certificate /path/to/cert.pem;
    # ssl_certificate_key /path/to/key.pem;
    
    # 前端静态文件
    location / {
        try_files \$uri \$uri/ /index.html;
        add_header Cache-Control "public, max-age=31536000";
    }
    
    # API 代理
    location /api/ {
        proxy_pass http://127.0.0.1:3000/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # WebSocket 支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 文件上传
    location /uploads/ {
        alias $PROJECT_DIR/uploads/;
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }
    
    # 安全配置
    location ~ /\\. {
        deny all;
    }
    
    # 文件大小限制
    client_max_body_size 10M;
    
    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
EOF
    
    # 重载 Nginx
    nginx -t && nginx -s reload
    
    log_success "Nginx 配置完成"
}

# 设置权限
set_permissions() {
    log_info "设置文件权限..."
    
    chown -R www:www "$PROJECT_DIR"
    chmod -R 755 "$PROJECT_DIR"
    
    # 创建上传目录
    mkdir -p "$PROJECT_DIR/uploads"
    chown -R www:www "$PROJECT_DIR/uploads"
    chmod -R 755 "$PROJECT_DIR/uploads"
    
    log_success "权限设置完成"
}

# 显示部署结果
show_result() {
    echo -e "${GREEN}"
    echo "=================================================="
    echo "🎉 部署完成！"
    echo "=================================================="
    echo -e "${NC}"
    echo "网站地址: http://$DOMAIN"
    echo "管理后台: http://$DOMAIN/boss"
    echo "工作台: http://$DOMAIN/employee"
    echo ""
    echo "数据库信息:"
    echo "  数据库名: $DB_NAME"
    echo "  用户名: $DB_USER"
    echo "  密码: $DB_PASSWORD"
    echo ""
    echo "ip2region离线IP库:"
    echo "  数据库文件: backend/src/data/ip2region.xdb"
    echo "  功能: 离线IP地理位置查询"
    echo "  性能: 单次查询 <50微秒"
    echo ""
    echo "常用命令:"
    echo "  查看服务状态: pm2 list"
    echo "  查看日志: pm2 logs game-boost-backend"
    echo "  重启服务: pm2 restart game-boost-backend"
    echo "  验证ip2region: bash scripts/verify-ip2region.sh"
    echo ""
    echo "请在宝塔面板中为域名配置 SSL 证书以启用 HTTPS"
}

# 主函数
main() {
    show_welcome
    check_environment
    collect_user_input
    create_project_directory
    setup_database
    deploy_backend
    deploy_frontend
    setup_pm2
    setup_nginx
    set_permissions
    show_result
}

# 运行主函数
main "$@"
