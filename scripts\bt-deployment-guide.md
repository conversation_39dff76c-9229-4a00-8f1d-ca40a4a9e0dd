# 🚀 宝塔面板部署完整指南 - 王者荣耀代练管理系统

## 📋 项目概述
- **项目名称**: 王者荣耀代练任务分发管理系统
- **技术栈**: Vue 3 + Node.js + MySQL + Redis + Socket.IO
- **部署方式**: 宝塔面板 + PM2 + Nginx

## 🛠️ 第一步：环境要求检查

### 服务器配置要求
- **CPU**: 2核心以上
- **内存**: 4GB以上 (推荐8GB)
- **硬盘**: 20GB以上可用空间
- **带宽**: 5Mbps以上
- **操作系统**: CentOS 7+ / Ubuntu 18+ / Debian 9+

### 宝塔面板软件要求
```bash
# 必装软件清单
✅ Nginx 1.18+
✅ MySQL 8.0+
✅ Node.js 18.x (通过Node.js版本管理器安装)
✅ PM2管理器
✅ Redis 7.x (可选，用于缓存)
✅ phpMyAdmin (数据库管理)
```

## 🔧 第二步：宝塔面板安装与配置

### 2.1 安装宝塔面板
```bash
# CentOS 系统
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh

# Ubuntu/Debian 系统
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh
```

### 2.2 登录并安装软件
1. 访问: `http://你的服务器IP:8888`
2. 使用安装时显示的用户名密码登录
3. 在软件商店安装必要软件

### 2.3 配置MySQL数据库
```sql
-- 创建数据库
CREATE DATABASE game_boost_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'game_boost_user'@'localhost' IDENTIFIED BY 'your_secure_password';

-- 授权
GRANT ALL PRIVILEGES ON game_boost_db.* TO 'game_boost_user'@'localhost';
FLUSH PRIVILEGES;
```

## 📁 第三步：项目部署

### 3.1 创建网站
1. 宝塔面板 → 网站 → 添加站点
2. 域名: `your-domain.com` (或IP地址)
3. 根目录: `/www/wwwroot/game-boost`
4. 选择纯静态

### 3.2 上传项目文件
```bash
# 方式1: 通过宝塔文件管理器上传项目压缩包
# 方式2: 使用Git克隆(推荐)
cd /www/wwwroot/game-boost
git clone https://your-repo-url.git .

# 设置权限
chown -R www:www /www/wwwroot/game-boost
chmod -R 755 /www/wwwroot/game-boost
```

### 3.3 配置后端服务

#### 安装依赖
```bash
cd /www/wwwroot/game-boost/backend
npm install --production
```

#### 创建环境配置
```bash
# 复制环境配置模板
cp .env.example .env

# 编辑配置文件
nano .env
```

#### 环境配置内容
```env
# 服务器配置
NODE_ENV=production
PORT=3000
HOST=localhost

# 数据库配置
DATABASE_URL="mysql://game_boost_user:your_secure_password@localhost:3306/game_boost_db"

# JWT配置
JWT_SECRET="your-super-secret-jwt-key-here"
JWT_EXPIRES_IN=7d

# 文件上传配置
UPLOAD_PATH=/www/wwwroot/game-boost/uploads
MAX_FILE_SIZE=10485760

# Socket.IO配置
SOCKET_CORS_ORIGIN=https://your-domain.com

# 日志配置
LOG_LEVEL=info
LOG_FILE=/www/wwwroot/game-boost/logs/app.log

# API配置
API_PREFIX=/api/v1
CORS_ORIGIN=https://your-domain.com
```

#### 初始化数据库
```bash
# 生成Prisma客户端
npx prisma generate

# 运行数据库迁移
npx prisma migrate deploy

# 构建后端项目
npm run build
```

### 3.4 配置前端项目

#### 安装依赖
```bash
cd /www/wwwroot/game-boost/frontend
npm install
```

#### 创建生产环境配置
```bash
cat > .env.production << EOF
# API基础地址
VITE_API_BASE_URL=https://your-domain.com/api/v1
VITE_APP_TITLE=王者荣耀代练管理系统

# 文件上传配置
VITE_UPLOAD_URL=https://your-domain.com/api/v1/upload
EOF
```

#### 构建前端项目
```bash
npm run build
```

## 🔄 第四步：配置PM2进程管理

### 4.1 创建PM2配置文件
```bash
cd /www/wwwroot/game-boost
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'game-boost-backend',
    script: './backend/dist/index.js',
    cwd: '/www/wwwroot/game-boost',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    restart_delay: 4000,
    watch: false,
    ignore_watch: ['node_modules', 'logs']
  }]
}
EOF
```

### 4.2 启动服务
```bash
# 创建日志目录
mkdir -p /www/wwwroot/game-boost/logs
mkdir -p /www/wwwroot/game-boost/uploads

# 设置权限
chown -R www:www /www/wwwroot/game-boost/logs
chown -R www:www /www/wwwroot/game-boost/uploads

# 启动PM2服务
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

## 🌐 第五步：配置Nginx

### 5.1 网站配置
在宝塔面板 → 网站 → 你的域名 → 设置 → 配置文件，替换为：

```nginx
server {
    listen 80;
    listen 443 ssl http2;
    server_name your-domain.com;
    
    root /www/wwwroot/game-boost/frontend/dist;
    index index.html;
    
    # SSL配置(在宝塔面板SSL选项卡配置)
    
    # 前端静态文件
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "public, max-age=31536000";
    }
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 文件上传访问
    location /uploads/ {
        alias /www/wwwroot/game-boost/uploads/;
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }
    
    # 安全配置
    location ~ /\. {
        deny all;
    }
    
    # 文件大小限制
    client_max_body_size 10M;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
```

## ✅ 第六步：验证部署

### 6.1 检查服务状态
```bash
# 检查PM2进程
pm2 list

# 检查端口占用
netstat -tlnp | grep 3000

# 检查Nginx状态
systemctl status nginx

# 检查MySQL状态
systemctl status mysql
```

### 6.2 测试访问
```bash
# 测试后端API
curl http://localhost:3000/api/v1/health

# 测试前端页面
curl http://your-domain.com
```

## 🔧 常用维护命令

```bash
# PM2管理
pm2 restart game-boost-backend  # 重启服务
pm2 logs game-boost-backend     # 查看日志
pm2 monit                       # 监控资源

# Nginx管理
nginx -t                        # 测试配置
nginx -s reload                 # 重载配置

# 数据库管理
mysql -u game_boost_user -p game_boost_db
```

## 🚨 故障排除

如遇问题，请运行诊断脚本：
```bash
bash scripts/deployment-check.sh your-domain.com
```

## 📞 技术支持

部署过程中如遇问题，请提供：
1. 错误日志内容
2. 服务器配置信息
3. 已完成的部署步骤
