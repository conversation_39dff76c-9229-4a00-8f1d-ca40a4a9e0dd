#!/bin/bash

# 🔍 宝塔面板部署前置检查脚本
# 王者荣耀代练管理系统部署环境检查

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔍 宝塔面板部署前置检查${NC}"
echo "=================================================="
echo "项目: 王者荣耀代练管理系统"
echo "检查时间: $(date)"
echo ""

# 检查系统信息
echo -e "${BLUE}📊 系统信息检查...${NC}"
echo "操作系统: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)"
echo "内核版本: $(uname -r)"
echo "架构: $(uname -m)"
echo ""

# 检查硬件资源
echo -e "${BLUE}💻 硬件资源检查...${NC}"

# CPU检查
CPU_CORES=$(nproc)
if [ "$CPU_CORES" -ge 2 ]; then
    echo -e "${GREEN}✅ CPU核心数: $CPU_CORES (满足要求)${NC}"
else
    echo -e "${RED}❌ CPU核心数: $CPU_CORES (建议2核心以上)${NC}"
fi

# 内存检查
MEMORY_GB=$(free -g | awk 'NR==2{print $2}')
if [ "$MEMORY_GB" -ge 4 ]; then
    echo -e "${GREEN}✅ 内存容量: ${MEMORY_GB}GB (满足要求)${NC}"
elif [ "$MEMORY_GB" -ge 2 ]; then
    echo -e "${YELLOW}⚠️  内存容量: ${MEMORY_GB}GB (建议4GB以上)${NC}"
else
    echo -e "${RED}❌ 内存容量: ${MEMORY_GB}GB (不满足要求)${NC}"
fi

# 磁盘空间检查
DISK_AVAILABLE=$(df -BG / | awk 'NR==2 {print $4}' | sed 's/G//')
if [ "$DISK_AVAILABLE" -ge 20 ]; then
    echo -e "${GREEN}✅ 可用磁盘空间: ${DISK_AVAILABLE}GB (满足要求)${NC}"
else
    echo -e "${RED}❌ 可用磁盘空间: ${DISK_AVAILABLE}GB (建议20GB以上)${NC}"
fi

echo ""

# 检查宝塔面板
echo -e "${BLUE}🎛️  宝塔面板检查...${NC}"
if [ -f "/www/server/panel/BT-Panel" ]; then
    echo -e "${GREEN}✅ 宝塔面板已安装${NC}"
    
    # 检查宝塔面板状态
    if systemctl is-active --quiet bt; then
        echo -e "${GREEN}✅ 宝塔面板服务运行中${NC}"
    else
        echo -e "${RED}❌ 宝塔面板服务未运行${NC}"
    fi
    
    # 检查宝塔面板版本
    if [ -f "/www/server/panel/class/common.py" ]; then
        BT_VERSION=$(grep -o "version = '[^']*'" /www/server/panel/class/common.py | cut -d"'" -f2)
        echo "宝塔面板版本: $BT_VERSION"
    fi
else
    echo -e "${RED}❌ 宝塔面板未安装${NC}"
    echo "安装命令:"
    echo "CentOS: yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh"
    echo "Ubuntu: wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh"
fi

echo ""

# 检查必要软件
echo -e "${BLUE}📦 必要软件检查...${NC}"

# 检查Nginx
if command -v nginx &> /dev/null; then
    NGINX_VERSION=$(nginx -v 2>&1 | grep -o '[0-9.]*')
    echo -e "${GREEN}✅ Nginx已安装: $NGINX_VERSION${NC}"
else
    echo -e "${RED}❌ Nginx未安装 (在宝塔面板软件商店安装)${NC}"
fi

# 检查MySQL
if command -v mysql &> /dev/null; then
    MYSQL_VERSION=$(mysql --version | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1)
    echo -e "${GREEN}✅ MySQL已安装: $MYSQL_VERSION${NC}"
    
    # 检查MySQL服务状态
    if systemctl is-active --quiet mysql || systemctl is-active --quiet mysqld; then
        echo -e "${GREEN}✅ MySQL服务运行中${NC}"
    else
        echo -e "${RED}❌ MySQL服务未运行${NC}"
    fi
else
    echo -e "${RED}❌ MySQL未安装 (在宝塔面板软件商店安装MySQL 8.0+)${NC}"
fi

# 检查Node.js
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    NODE_MAJOR=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
    if [ "$NODE_MAJOR" -ge 16 ]; then
        echo -e "${GREEN}✅ Node.js已安装: $NODE_VERSION (满足要求)${NC}"
    else
        echo -e "${YELLOW}⚠️  Node.js版本: $NODE_VERSION (建议16.x或18.x)${NC}"
    fi
else
    echo -e "${RED}❌ Node.js未安装 (在宝塔面板软件商店安装Node.js版本管理器)${NC}"
fi

# 检查npm
if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm --version)
    echo -e "${GREEN}✅ npm已安装: $NPM_VERSION${NC}"
else
    echo -e "${RED}❌ npm未安装 (随Node.js一起安装)${NC}"
fi

# 检查PM2
if command -v pm2 &> /dev/null; then
    PM2_VERSION=$(pm2 --version)
    echo -e "${GREEN}✅ PM2已安装: $PM2_VERSION${NC}"
else
    echo -e "${RED}❌ PM2未安装 (在宝塔面板软件商店安装PM2管理器)${NC}"
fi

# 检查Redis (可选)
if command -v redis-server &> /dev/null; then
    REDIS_VERSION=$(redis-server --version | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1)
    echo -e "${GREEN}✅ Redis已安装: $REDIS_VERSION (可选)${NC}"
else
    echo -e "${YELLOW}⚠️  Redis未安装 (可选，用于缓存优化)${NC}"
fi

echo ""

# 检查端口占用
echo -e "${BLUE}🔌 端口检查...${NC}"

# 检查常用端口
PORTS=(80 443 3000 3306 6379 8888)
for port in "${PORTS[@]}"; do
    if netstat -tlnp | grep -q ":$port "; then
        case $port in
            80) echo -e "${GREEN}✅ 端口 $port (HTTP) 已被占用${NC}" ;;
            443) echo -e "${GREEN}✅ 端口 $port (HTTPS) 已被占用${NC}" ;;
            3000) echo -e "${YELLOW}⚠️  端口 $port (后端) 已被占用 (部署时需要检查)${NC}" ;;
            3306) echo -e "${GREEN}✅ 端口 $port (MySQL) 已被占用${NC}" ;;
            6379) echo -e "${GREEN}✅ 端口 $port (Redis) 已被占用${NC}" ;;
            8888) echo -e "${GREEN}✅ 端口 $port (宝塔面板) 已被占用${NC}" ;;
        esac
    else
        case $port in
            80|443|3306|8888) echo -e "${RED}❌ 端口 $port 未被占用 (相关服务可能未启动)${NC}" ;;
            3000) echo -e "${GREEN}✅ 端口 $port (后端) 可用${NC}" ;;
            6379) echo -e "${YELLOW}⚠️  端口 $port (Redis) 未被占用 (如需Redis请安装)${NC}" ;;
        esac
    fi
done

echo ""

# 检查防火墙
echo -e "${BLUE}🔥 防火墙检查...${NC}"
if command -v ufw &> /dev/null; then
    UFW_STATUS=$(ufw status | head -1)
    echo "UFW状态: $UFW_STATUS"
elif command -v firewall-cmd &> /dev/null; then
    FIREWALL_STATUS=$(firewall-cmd --state 2>/dev/null || echo "inactive")
    echo "Firewall状态: $FIREWALL_STATUS"
else
    echo "未检测到常见防火墙工具"
fi

echo ""

# 检查项目目录
echo -e "${BLUE}📁 项目目录检查...${NC}"
PROJECT_DIR="/www/wwwroot/game-boost"
if [ -d "$PROJECT_DIR" ]; then
    echo -e "${GREEN}✅ 项目目录存在: $PROJECT_DIR${NC}"
    
    # 检查目录权限
    OWNER=$(stat -c '%U:%G' "$PROJECT_DIR")
    echo "目录所有者: $OWNER"
    
    # 检查子目录
    if [ -d "$PROJECT_DIR/frontend" ]; then
        echo -e "${GREEN}✅ 前端目录存在${NC}"
    else
        echo -e "${YELLOW}⚠️  前端目录不存在${NC}"
    fi
    
    if [ -d "$PROJECT_DIR/backend" ]; then
        echo -e "${GREEN}✅ 后端目录存在${NC}"
    else
        echo -e "${YELLOW}⚠️  后端目录不存在${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  项目目录不存在: $PROJECT_DIR${NC}"
    echo "建议在宝塔面板创建网站，根目录设为: $PROJECT_DIR"
fi

echo ""

# 总结建议
echo -e "${BLUE}📋 部署建议总结${NC}"
echo "=================================================="
echo "1. 确保所有必要软件已在宝塔面板安装"
echo "2. 配置MySQL数据库和用户权限"
echo "3. 上传项目文件到 /www/wwwroot/game-boost"
echo "4. 按照部署指南逐步配置"
echo ""
echo "详细部署指南: scripts/bt-deployment-guide.md"
echo "自动部署脚本: scripts/bt-deploy-helper.sh"
