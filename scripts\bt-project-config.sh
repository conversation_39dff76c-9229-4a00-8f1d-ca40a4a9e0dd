#!/bin/bash

# 🎯 王者荣耀代练管理系统 - 宝塔部署配置脚本
# 专门针对本项目的配置优化

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 项目配置
PROJECT_NAME="game-boost"
PROJECT_DIR="/www/wwwroot/${PROJECT_NAME}"
DOMAIN=${1:-""}

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 显示项目信息
show_project_info() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "🎮 王者荣耀代练管理系统 - 宝塔部署配置"
    echo "=================================================="
    echo -e "${NC}"
    echo "项目特点:"
    echo "  ✅ Vue 3 + TypeScript 前端"
    echo "  ✅ Node.js + Express + Prisma 后端"
    echo "  ✅ MySQL 8.0+ 数据库"
    echo "  ✅ Socket.IO 实时通信"
    echo "  ✅ PM2 进程管理"
    echo "  ✅ Redis 缓存支持(可选)"
    echo ""
}

# 检查项目结构
check_project_structure() {
    log_info "检查项目结构..."
    
    if [ ! -d "$PROJECT_DIR" ]; then
        log_error "项目目录不存在: $PROJECT_DIR"
        log_info "请先在宝塔面板创建网站，根目录设为: $PROJECT_DIR"
        exit 1
    fi
    
    # 检查关键文件
    local required_files=(
        "frontend/package.json"
        "backend/package.json"
        "backend/prisma/schema.prisma"
        "backend/.env.example"
    )
    
    for file in "${required_files[@]}"; do
        if [ -f "$PROJECT_DIR/$file" ]; then
            log_success "✅ $file 存在"
        else
            log_error "❌ $file 不存在"
            exit 1
        fi
    done
    
    log_success "项目结构检查完成"
}

# 配置数据库
setup_database_interactive() {
    log_info "配置数据库..."
    
    # 获取数据库配置
    echo "请输入数据库配置信息:"
    read -p "数据库名称 [game_boost_db]: " db_name
    db_name=${db_name:-game_boost_db}
    
    read -p "数据库用户名 [game_boost_user]: " db_user
    db_user=${db_user:-game_boost_user}
    
    read -s -p "数据库密码: " db_password
    echo
    
    if [ -z "$db_password" ]; then
        log_error "数据库密码不能为空"
        exit 1
    fi
    
    # 测试数据库连接
    log_info "测试数据库连接..."
    if mysql -e "SELECT 1;" 2>/dev/null; then
        log_success "MySQL连接成功"
        
        # 创建数据库和用户
        mysql << EOF
CREATE DATABASE IF NOT EXISTS $db_name CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS '$db_user'@'localhost' IDENTIFIED BY '$db_password';
GRANT ALL PRIVILEGES ON $db_name.* TO '$db_user'@'localhost';
FLUSH PRIVILEGES;
EOF
        
        log_success "数据库配置完成"
        
        # 保存配置到临时文件
        cat > /tmp/db_config << EOF
DB_NAME=$db_name
DB_USER=$db_user
DB_PASSWORD=$db_password
EOF
        
    else
        log_error "MySQL连接失败，请检查MySQL服务状态"
        exit 1
    fi
}

# 配置后端环境
setup_backend_env() {
    log_info "配置后端环境..."
    
    cd "$PROJECT_DIR/backend"
    
    # 读取数据库配置
    source /tmp/db_config
    
    # 生成JWT密钥
    JWT_SECRET=$(openssl rand -base64 32)
    
    # 获取域名
    if [ -z "$DOMAIN" ]; then
        read -p "请输入域名或IP地址: " DOMAIN
    fi
    
    # 创建环境配置文件
    cat > .env << EOF
# 服务器配置
NODE_ENV=production
PORT=3000
HOST=localhost

# 数据库配置
DATABASE_URL="mysql://$DB_USER:$DB_PASSWORD@localhost:3306/$DB_NAME"

# JWT配置
JWT_SECRET="$JWT_SECRET"
JWT_EXPIRES_IN=7d

# 文件上传配置
UPLOAD_PATH=$PROJECT_DIR/uploads
MAX_FILE_SIZE=10485760

# Socket.IO配置
SOCKET_CORS_ORIGIN=https://$DOMAIN

# 日志配置
LOG_LEVEL=info
LOG_FILE=$PROJECT_DIR/logs/app.log

# API配置
API_PREFIX=/api/v1
CORS_ORIGIN=https://$DOMAIN

# Redis配置(可选)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
EOF
    
    log_success "后端环境配置完成"
}

# 配置前端环境
setup_frontend_env() {
    log_info "配置前端环境..."
    
    cd "$PROJECT_DIR/frontend"
    
    # 创建生产环境配置
    cat > .env.production << EOF
# API基础地址
VITE_API_BASE_URL=https://$DOMAIN/api/v1
VITE_APP_TITLE=王者荣耀代练管理系统

# 文件上传配置
VITE_UPLOAD_URL=https://$DOMAIN/api/v1/upload

# Socket.IO配置
VITE_SOCKET_URL=https://$DOMAIN
EOF
    
    log_success "前端环境配置完成"
}

# 配置ip2region离线IP库
setup_ip2region() {
    log_info "配置ip2region离线IP地址库..."

    # 检查ip2region数据文件
    local ip2region_source="$PROJECT_DIR/ip2region-master/data/ip2region.xdb"
    local ip2region_target="$PROJECT_DIR/backend/src/data/ip2region.xdb"

    if [ -f "$ip2region_source" ]; then
        log_info "复制ip2region数据库文件..."
        mkdir -p "$(dirname "$ip2region_target")"
        cp "$ip2region_source" "$ip2region_target"
        chmod 644 "$ip2region_target"
        log_success "ip2region数据库文件配置完成"
    else
        log_error "ip2region数据库文件不存在: $ip2region_source"
        log_info "请确保ip2region-master目录已正确上传"
        exit 1
    fi

    # 检查Node.js绑定库
    local nodejs_binding_source="$PROJECT_DIR/ip2region-master/binding/nodejs"
    local nodejs_binding_target="$PROJECT_DIR/backend/src/lib"

    if [ -d "$nodejs_binding_source" ]; then
        log_info "配置ip2region Node.js绑定库..."
        mkdir -p "$nodejs_binding_target"
        cp "$nodejs_binding_source/index.js" "$nodejs_binding_target/"
        cp "$nodejs_binding_source/package.json" "$nodejs_binding_target/"
        log_success "ip2region Node.js绑定库配置完成"
    else
        log_warning "ip2region Node.js绑定库目录不存在，使用项目内置版本"
    fi

    # 验证ip2region配置
    if [ -f "$ip2region_target" ] && [ -f "$nodejs_binding_target/index.js" ]; then
        local file_size=$(stat -c%s "$ip2region_target" 2>/dev/null || stat -f%z "$ip2region_target" 2>/dev/null)
        log_success "ip2region配置验证通过 (数据库大小: ${file_size} bytes)"
    else
        log_error "ip2region配置验证失败"
        exit 1
    fi
}

# 安装依赖和构建
build_project() {
    log_info "安装依赖和构建项目..."

    # 构建后端
    log_info "构建后端..."
    cd "$PROJECT_DIR/backend"

    # 检查Node.js版本
    NODE_VERSION=$(node --version | cut -d'.' -f1 | sed 's/v//')
    if [ "$NODE_VERSION" -lt 16 ]; then
        log_warning "Node.js版本过低: v$NODE_VERSION，建议使用16.x或18.x"
    fi

    npm install --production

    # 生成Prisma客户端
    npx prisma generate

    # 运行数据库迁移
    log_info "运行数据库迁移..."
    npx prisma migrate deploy

    # 构建TypeScript
    npm run build

    log_success "后端构建完成"

    # 构建前端
    log_info "构建前端..."
    cd "$PROJECT_DIR/frontend"

    npm install
    npm run build

    log_success "前端构建完成"
}

# 创建PM2配置
create_pm2_config() {
    log_info "创建PM2配置..."
    
    cd "$PROJECT_DIR"
    
    cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'game-boost-backend',
    script: './backend/dist/index.js',
    cwd: '$PROJECT_DIR',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    restart_delay: 4000,
    watch: false,
    ignore_watch: ['node_modules', 'logs', 'uploads'],
    // 健康检查
    health_check_url: 'http://localhost:3000/api/v1/health',
    health_check_grace_period: 3000
  }]
}
EOF
    
    # 创建必要目录
    mkdir -p logs uploads
    chown -R www:www logs uploads
    chmod -R 755 logs uploads
    
    log_success "PM2配置创建完成"
}

# 生成Nginx配置
generate_nginx_config() {
    log_info "生成Nginx配置建议..."
    
    cat > "$PROJECT_DIR/nginx.conf.example" << EOF
# 王者荣耀代练管理系统 - Nginx配置
# 请将此配置复制到宝塔面板网站配置中

server {
    listen 80;
    listen 443 ssl http2;
    server_name $DOMAIN;
    
    root $PROJECT_DIR/frontend/dist;
    index index.html;
    
    # SSL配置(在宝塔面板SSL选项卡配置)
    
    # 前端静态文件
    location / {
        try_files \$uri \$uri/ /index.html;
        add_header Cache-Control "public, max-age=31536000";
    }
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:3000/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 文件上传访问
    location /uploads/ {
        alias $PROJECT_DIR/uploads/;
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }
    
    # 安全配置
    location ~ /\\. {
        deny all;
    }
    
    # 文件大小限制
    client_max_body_size 10M;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
}
EOF
    
    log_success "Nginx配置已生成: $PROJECT_DIR/nginx.conf.example"
}

# 设置权限
set_permissions() {
    log_info "设置文件权限..."
    
    chown -R www:www "$PROJECT_DIR"
    chmod -R 755 "$PROJECT_DIR"
    
    # 特殊权限设置
    chmod 600 "$PROJECT_DIR/backend/.env"
    chmod +x "$PROJECT_DIR/backend/dist/index.js" 2>/dev/null || true
    
    log_success "权限设置完成"
}

# 显示部署结果
show_deployment_result() {
    echo -e "${GREEN}"
    echo "=================================================="
    echo "🎉 项目配置完成！"
    echo "=================================================="
    echo -e "${NC}"
    echo "接下来的步骤:"
    echo ""
    echo "1. 启动PM2服务:"
    echo "   cd $PROJECT_DIR"
    echo "   pm2 start ecosystem.config.js"
    echo "   pm2 save"
    echo ""
    echo "2. 配置Nginx:"
    echo "   - 复制 $PROJECT_DIR/nginx.conf.example 的内容"
    echo "   - 粘贴到宝塔面板 → 网站 → $DOMAIN → 设置 → 配置文件"
    echo "   - 重载Nginx配置"
    echo ""
    echo "3. 配置SSL证书(推荐):"
    echo "   - 在宝塔面板 → 网站 → $DOMAIN → SSL"
    echo "   - 申请Let's Encrypt证书"
    echo ""
    echo "4. 测试访问:"
    echo "   - 前端: https://$DOMAIN"
    echo "   - API健康检查: https://$DOMAIN/api/v1/health"
    echo ""
    echo "5. 常用管理命令:"
    echo "   - 查看服务: pm2 list"
    echo "   - 查看日志: pm2 logs game-boost-backend"
    echo "   - 重启服务: pm2 restart game-boost-backend"
    echo ""
    echo "📞 如遇问题，请运行: bash scripts/deployment-check.sh $DOMAIN"
}

# 主函数
main() {
    show_project_info
    check_project_structure
    setup_database_interactive
    setup_backend_env
    setup_frontend_env
    setup_ip2region
    build_project
    create_pm2_config
    generate_nginx_config
    set_permissions
    show_deployment_result

    # 清理临时文件
    rm -f /tmp/db_config
}

# 运行主函数
main "$@"
