@echo off
setlocal enabledelayedexpansion

:: Backup Files Cleanup Script (Batch Version)
:: Delete all .backup-* format backup files

echo.
echo Backup Files Cleanup Tool
echo.

:: 检查参数
set "DRY_RUN=false"
set "FORCE=false"
set "VERBOSE=false"

:parse_args
if "%~1"=="" goto start_cleanup
if /i "%~1"=="--dry-run" set "DRY_RUN=true"
if /i "%~1"=="-d" set "DRY_RUN=true"
if /i "%~1"=="--force" set "FORCE=true"
if /i "%~1"=="-f" set "FORCE=true"
if /i "%~1"=="--verbose" set "VERBOSE=true"
if /i "%~1"=="-v" set "VERBOSE=true"
if /i "%~1"=="--help" goto show_help
if /i "%~1"=="-h" goto show_help
shift
goto parse_args

:show_help
echo 用法: cleanup-backup-files.bat [选项]
echo.
echo 选项:
echo   -d, --dry-run     干运行模式，只显示要删除的文件，不实际删除
echo   -f, --force       强制删除，不询问确认
echo   -v, --verbose     显示详细信息
echo   -h, --help        显示此帮助信息
echo.
echo 示例:
echo   cleanup-backup-files.bat --dry-run    # 查看要删除的文件
echo   cleanup-backup-files.bat --force      # 直接删除所有备份文件
echo   cleanup-backup-files.bat -v           # 显示详细过程
echo.
goto end

:start_cleanup
:: 显示配置信息
if "%VERBOSE%"=="true" (
    echo ℹ️ 配置信息:
    echo   干运行模式: %DRY_RUN%
    echo   强制模式: %FORCE%
    echo.
)

echo ℹ️ 开始扫描备份文件...

:: 创建临时文件来存储找到的备份文件
set "TEMP_FILE=%TEMP%\backup_files_%RANDOM%.txt"
if exist "%TEMP_FILE%" del "%TEMP_FILE%"

set "FILE_COUNT=0"
set "TOTAL_SIZE=0"

:: 扫描目录列表
set "SCAN_DIRS=frontend\src\views frontend\src\components frontend\src\stores frontend\src\api frontend\src\utils backend\src"

:: 备份文件模式
set "PATTERNS=*.backup-footer-* *.backup-standalone-* *.backup-fix-* *.backup-* *.bak *.old"

:: 扫描每个目录
for %%d in (%SCAN_DIRS%) do (
    if exist "%%d" (
        if "%VERBOSE%"=="true" echo 🔍 扫描目录: %%d
        
        for %%p in (%PATTERNS%) do (
            for /r "%%d" %%f in ("%%p") do (
                if exist "%%f" (
                    echo %%f >> "%TEMP_FILE%"
                    set /a FILE_COUNT+=1
                    if "%VERBOSE%"=="true" echo 🔍 发现备份文件: %%f
                )
            )
        )
    ) else (
        echo ⚠️ 目录不存在: %%d
    )
)

:: 检查是否找到文件
if %FILE_COUNT%==0 (
    echo ✅ 没有发现备份文件，目录已经很干净了！
    goto cleanup_temp
)

echo.
echo 📋 发现 %FILE_COUNT% 个备份文件:

:: 显示找到的文件
set "INDEX=0"
for /f "delims=" %%f in (%TEMP_FILE%) do (
    set /a INDEX+=1
    echo   !INDEX!. %%f
)

echo.
echo 📊 总计: %FILE_COUNT% 个文件
echo.

:: 干运行模式
if "%DRY_RUN%"=="true" (
    echo ⚠️ 干运行模式：以上文件将被删除 ^(使用 --force 实际执行删除^)
    goto cleanup_temp
)

:: 确认删除
if "%FORCE%"=="false" (
    set /p "CONFIRM=⚠️ 确定要删除这些备份文件吗？(y/N): "
    if /i not "!CONFIRM!"=="y" if /i not "!CONFIRM!"=="yes" (
        echo ℹ️ 操作已取消
        goto cleanup_temp
    )
)

:: 执行删除
echo ℹ️ 开始删除备份文件...

set "DELETED_COUNT=0"
set "FAILED_COUNT=0"

for /f "delims=" %%f in (%TEMP_FILE%) do (
    if exist "%%f" (
        del "%%f" 2>nul
        if !errorlevel!==0 (
            set /a DELETED_COUNT+=1
            if "%VERBOSE%"=="true" echo 🔍 已删除: %%f
        ) else (
            set /a FAILED_COUNT+=1
            echo ❌ 删除失败: %%f
        )
    )
)

:: 显示结果
echo.
if %DELETED_COUNT% gtr 0 (
    echo ✅ 成功删除 %DELETED_COUNT% 个备份文件
)
if %FAILED_COUNT% gtr 0 (
    echo ❌ 删除失败 %FAILED_COUNT% 个文件
)

echo ✅ 清理完成！

:cleanup_temp
:: 清理临时文件
if exist "%TEMP_FILE%" del "%TEMP_FILE%"

:end
echo.
pause
