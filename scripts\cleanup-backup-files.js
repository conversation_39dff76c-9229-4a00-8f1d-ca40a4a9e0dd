#!/usr/bin/env node

/**
 * 清理备份文件脚本
 * 删除所有 .backup-* 格式的备份文件
 */

const fs = require('fs');
const path = require('path');

// 配置
const config = {
  // 要扫描的目录
  scanDirectories: [
    'frontend/src/views',
    'frontend/src/components',
    'frontend/src/stores',
    'frontend/src/api',
    'frontend/src/utils',
    'backend/src'
  ],
  
  // 备份文件的匹配模式
  backupPatterns: [
    /\.backup-footer-\d+$/,
    /\.backup-standalone-\d+$/,
    /\.backup-fix-\d+$/,
    /\.backup-\d+$/,
    /\.bak$/,
    /\.old$/
  ],
  
  // 是否进行干运行（只显示要删除的文件，不实际删除）
  dryRun: process.argv.includes('--dry-run') || process.argv.includes('-d'),
  
  // 是否显示详细信息
  verbose: process.argv.includes('--verbose') || process.argv.includes('-v'),
  
  // 是否强制删除（不询问确认）
  force: process.argv.includes('--force') || process.argv.includes('-f')
};

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m'
};

// 日志函数
const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✅${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}❌${colors.reset} ${msg}`),
  debug: (msg) => config.verbose && console.log(`${colors.cyan}🔍${colors.reset} ${msg}`)
};

// 递归扫描目录
function scanDirectory(dirPath, backupFiles = []) {
  try {
    if (!fs.existsSync(dirPath)) {
      log.warning(`目录不存在: ${dirPath}`);
      return backupFiles;
    }

    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // 跳过 node_modules 和 .git 目录
        if (item !== 'node_modules' && item !== '.git' && !item.startsWith('.')) {
          scanDirectory(fullPath, backupFiles);
        }
      } else if (stat.isFile()) {
        // 检查是否是备份文件
        const isBackupFile = config.backupPatterns.some(pattern => 
          pattern.test(item)
        );
        
        if (isBackupFile) {
          backupFiles.push(fullPath);
          log.debug(`发现备份文件: ${fullPath}`);
        }
      }
    }
  } catch (error) {
    log.error(`扫描目录失败 ${dirPath}: ${error.message}`);
  }
  
  return backupFiles;
}

// 删除文件
function deleteFile(filePath) {
  try {
    fs.unlinkSync(filePath);
    return true;
  } catch (error) {
    log.error(`删除文件失败 ${filePath}: ${error.message}`);
    return false;
  }
}

// 格式化文件大小
function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 获取文件信息
function getFileInfo(filePath) {
  try {
    const stat = fs.statSync(filePath);
    return {
      size: stat.size,
      mtime: stat.mtime,
      exists: true
    };
  } catch (error) {
    return {
      size: 0,
      mtime: null,
      exists: false
    };
  }
}

// 主函数
async function main() {
  console.log(`${colors.magenta}🧹 备份文件清理工具${colors.reset}\n`);
  
  // 显示配置
  if (config.verbose) {
    log.info('配置信息:');
    console.log(`  扫描目录: ${config.scanDirectories.join(', ')}`);
    console.log(`  干运行模式: ${config.dryRun ? '是' : '否'}`);
    console.log(`  强制模式: ${config.force ? '是' : '否'}`);
    console.log('');
  }
  
  // 扫描所有目录
  log.info('开始扫描备份文件...');
  let allBackupFiles = [];
  
  for (const dir of config.scanDirectories) {
    log.debug(`扫描目录: ${dir}`);
    const backupFiles = scanDirectory(dir);
    allBackupFiles = allBackupFiles.concat(backupFiles);
  }
  
  if (allBackupFiles.length === 0) {
    log.success('没有发现备份文件，目录已经很干净了！');
    return;
  }
  
  // 统计信息
  let totalSize = 0;
  const fileInfos = allBackupFiles.map(file => {
    const info = getFileInfo(file);
    totalSize += info.size;
    return { path: file, ...info };
  });
  
  // 显示发现的文件
  console.log(`\n${colors.yellow}📋 发现 ${allBackupFiles.length} 个备份文件:${colors.reset}`);
  fileInfos.forEach((file, index) => {
    const size = formatFileSize(file.size);
    const date = file.mtime ? file.mtime.toLocaleString('zh-CN') : '未知';
    console.log(`  ${index + 1}. ${file.path} (${size}, ${date})`);
  });
  
  console.log(`\n${colors.cyan}📊 总计: ${allBackupFiles.length} 个文件, ${formatFileSize(totalSize)}${colors.reset}\n`);
  
  // 干运行模式
  if (config.dryRun) {
    log.warning('干运行模式：以上文件将被删除（使用 --force 实际执行删除）');
    return;
  }
  
  // 确认删除
  if (!config.force) {
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    const answer = await new Promise(resolve => {
      rl.question(`${colors.yellow}⚠️  确定要删除这些备份文件吗？(y/N): ${colors.reset}`, resolve);
    });
    
    rl.close();
    
    if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
      log.info('操作已取消');
      return;
    }
  }
  
  // 执行删除
  log.info('开始删除备份文件...');
  let deletedCount = 0;
  let failedCount = 0;
  
  for (const file of allBackupFiles) {
    if (deleteFile(file)) {
      deletedCount++;
      log.debug(`已删除: ${file}`);
    } else {
      failedCount++;
    }
  }
  
  // 显示结果
  console.log('');
  if (deletedCount > 0) {
    log.success(`成功删除 ${deletedCount} 个备份文件`);
  }
  if (failedCount > 0) {
    log.error(`删除失败 ${failedCount} 个文件`);
  }
  
  log.success('清理完成！');
}

// 显示帮助信息
function showHelp() {
  console.log(`${colors.magenta}🧹 备份文件清理工具${colors.reset}

用法: node cleanup-backup-files.js [选项]

选项:
  -d, --dry-run     干运行模式，只显示要删除的文件，不实际删除
  -f, --force       强制删除，不询问确认
  -v, --verbose     显示详细信息
  -h, --help        显示此帮助信息

示例:
  node cleanup-backup-files.js --dry-run    # 查看要删除的文件
  node cleanup-backup-files.js --force      # 直接删除所有备份文件
  node cleanup-backup-files.js -v           # 显示详细过程
`);
}

// 处理命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  showHelp();
  process.exit(0);
}

// 运行主函数
main().catch(error => {
  log.error(`脚本执行失败: ${error.message}`);
  process.exit(1);
});
