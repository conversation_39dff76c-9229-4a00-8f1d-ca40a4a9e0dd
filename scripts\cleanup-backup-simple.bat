@echo off
setlocal enabledelayedexpansion

echo.
echo Backup Files Cleanup Tool
echo.

:: Check for dry-run parameter
set "DRY_RUN=false"
if "%1"=="--dry-run" set "DRY_RUN=true"
if "%1"=="-d" set "DRY_RUN=true"

echo Scanning for backup files...
echo.

:: Create temp file to store found files
set "TEMP_FILE=%TEMP%\backup_files_%RANDOM%.txt"
if exist "%TEMP_FILE%" del "%TEMP_FILE%"

:: Find backup files
echo Searching in frontend\src\views...
if exist "frontend\src\views" (
    for /r "frontend\src\views" %%f in (*.backup-*) do echo %%f >> "%TEMP_FILE%"
)

echo Searching in frontend\src\components...
if exist "frontend\src\components" (
    for /r "frontend\src\components" %%f in (*.backup-*) do echo %%f >> "%TEMP_FILE%"
)

echo Searching in frontend\src\stores...
if exist "frontend\src\stores" (
    for /r "frontend\src\stores" %%f in (*.backup-*) do echo %%f >> "%TEMP_FILE%"
)

echo Searching in frontend\src\api...
if exist "frontend\src\api" (
    for /r "frontend\src\api" %%f in (*.backup-*) do echo %%f >> "%TEMP_FILE%"
)

echo Searching in backend\src...
if exist "backend\src" (
    for /r "backend\src" %%f in (*.backup-*) do echo %%f >> "%TEMP_FILE%"
)

:: Count files
set "FILE_COUNT=0"
if exist "%TEMP_FILE%" (
    for /f %%i in ('type "%TEMP_FILE%" 2^>nul ^| find /c /v ""') do set "FILE_COUNT=%%i"
)

echo.
if %FILE_COUNT%==0 (
    echo No backup files found. Directory is clean!
    goto cleanup
)

echo Found %FILE_COUNT% backup files:
echo.

:: Display found files
set "INDEX=0"
for /f "delims=" %%f in (%TEMP_FILE%) do (
    set /a INDEX+=1
    echo   !INDEX!. %%f
)

echo.

:: Dry run mode
if "%DRY_RUN%"=="true" (
    echo DRY RUN MODE: Above files would be deleted
    echo Use without --dry-run to actually delete files
    goto cleanup
)

:: Confirm deletion
set /p "CONFIRM=Are you sure you want to delete these backup files? (y/N): "
if /i not "%CONFIRM%"=="y" if /i not "%CONFIRM%"=="yes" (
    echo Operation cancelled
    goto cleanup
)

:: Delete files
echo.
echo Deleting backup files...

set "DELETED_COUNT=0"
set "FAILED_COUNT=0"

for /f "delims=" %%f in (%TEMP_FILE%) do (
    if exist "%%f" (
        del "%%f" 2>nul
        if !errorlevel!==0 (
            set /a DELETED_COUNT+=1
            echo Deleted: %%f
        ) else (
            set /a FAILED_COUNT+=1
            echo Failed to delete: %%f
        )
    )
)

echo.
if %DELETED_COUNT% gtr 0 (
    echo Successfully deleted %DELETED_COUNT% backup files
)
if %FAILED_COUNT% gtr 0 (
    echo Failed to delete %FAILED_COUNT% files
)

echo Cleanup completed!

:cleanup
if exist "%TEMP_FILE%" del "%TEMP_FILE%"

echo.
pause
