#!/usr/bin/env node

/**
 * 备份文件清理工具
 * 
 * 功能：
 * - 查找并删除所有版权工具创建的备份文件
 * - 支持多种备份文件格式
 * - 提供安全确认机制
 * - 显示详细的清理统计
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

// 备份文件模式
const BACKUP_PATTERNS = [
  /\.backup-\d+$/,           // .backup-时间戳
  /\.backup-remove$/,        // .backup-remove
  /\.backup-add$/,           // .backup-add
  /\.backup-demo$/           // .backup-demo
];

// 排除目录
const EXCLUDE_DIRS = [
  'node_modules',
  '.git',
  'dist',
  'build',
  '.next',
  '.nuxt',
  'coverage'
];

class BackupCleaner {
  constructor() {
    this.foundBackups = [];
    this.deletedCount = 0;
    this.totalSize = 0;
  }

  // 检查是否为备份文件
  isBackupFile(fileName) {
    return BACKUP_PATTERNS.some(pattern => pattern.test(fileName));
  }

  // 检查是否应该排除的目录
  shouldExcludeDir(dirName) {
    return EXCLUDE_DIRS.includes(dirName) || dirName.startsWith('.');
  }

  // 递归扫描目录
  scanDirectory(dirPath) {
    try {
      const items = fs.readdirSync(dirPath);
      
      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          if (!this.shouldExcludeDir(item)) {
            this.scanDirectory(fullPath);
          }
        } else if (stat.isFile() && this.isBackupFile(item)) {
          this.foundBackups.push({
            path: fullPath,
            size: stat.size,
            modified: stat.mtime
          });
          this.totalSize += stat.size;
        }
      }
    } catch (error) {
      console.log(`⚠️  无法访问目录: ${dirPath} (${error.message})`);
    }
  }

  // 格式化文件大小
  formatSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // 显示找到的备份文件
  displayFoundBackups() {
    if (this.foundBackups.length === 0) {
      console.log('✅ 没有找到任何备份文件');
      return false;
    }

    console.log(`\n📁 找到 ${this.foundBackups.length} 个备份文件，总大小: ${this.formatSize(this.totalSize)}\n`);
    
    // 按目录分组显示
    const groupedBackups = {};
    this.foundBackups.forEach(backup => {
      const dir = path.dirname(backup.path);
      if (!groupedBackups[dir]) {
        groupedBackups[dir] = [];
      }
      groupedBackups[dir].push(backup);
    });

    Object.keys(groupedBackups).sort().forEach(dir => {
      console.log(`📂 ${dir}/`);
      groupedBackups[dir].forEach(backup => {
        const fileName = path.basename(backup.path);
        const size = this.formatSize(backup.size);
        const date = backup.modified.toLocaleString();
        console.log(`   🗃️  ${fileName} (${size}, ${date})`);
      });
      console.log('');
    });

    return true;
  }

  // 删除备份文件
  async deleteBackups() {
    console.log('🗑️  开始删除备份文件...\n');
    
    for (const backup of this.foundBackups) {
      try {
        fs.unlinkSync(backup.path);
        this.deletedCount++;
        console.log(`✅ 已删除: ${backup.path}`);
      } catch (error) {
        console.log(`❌ 删除失败: ${backup.path} (${error.message})`);
      }
    }
  }

  // 用户确认
  async getUserConfirmation() {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    return new Promise((resolve) => {
      rl.question('\n❓ 确定要删除这些备份文件吗？输入 "YES" 确认删除: ', (answer) => {
        rl.close();
        resolve(answer.trim() === 'YES');
      });
    });
  }

  // 主执行函数
  async run() {
    console.log('🧹 Ace Platform 备份文件清理工具');
    console.log('=====================================\n');
    
    console.log('🔍 正在扫描备份文件...');
    this.scanDirectory('.');
    
    const hasBackups = this.displayFoundBackups();
    
    if (!hasBackups) {
      return;
    }

    const confirmed = await this.getUserConfirmation();
    
    if (!confirmed) {
      console.log('\n❌ 操作已取消');
      return;
    }

    await this.deleteBackups();
    
    console.log('\n📈 清理结果统计:');
    console.log(`✅ 成功删除: ${this.deletedCount} 个文件`);
    console.log(`💾 释放空间: ${this.formatSize(this.totalSize)}`);
    console.log(`⏭️  跳过文件: ${this.foundBackups.length - this.deletedCount} 个文件`);
    
    if (this.deletedCount > 0) {
      console.log('\n🎉 备份文件清理完成！');
    }
  }
}

// 运行清理工具
const cleaner = new BackupCleaner();
cleaner.run().catch(error => {
  console.error('❌ 清理过程中发生错误:', error);
  process.exit(1);
});
