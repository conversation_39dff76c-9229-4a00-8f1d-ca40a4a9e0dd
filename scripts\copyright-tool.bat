@echo off
chcp 65001 >nul
title Ace Platform 版权管理工具

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    Ace Platform 版权管理工具                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:MENU
echo 请选择操作：
echo.
echo [1] 首次设置认证密钥
echo [2] 为所有代码添加版权信息
echo [3] 删除所有版权信息
echo [4] 演示版权添加
echo [5] 演示版权删除
echo [6] 清理备份文件
echo [7] 查看使用说明
echo [8] 退出
echo.
set /p choice=请输入选项 (1-8):

if "%choice%"=="1" goto SETUP_AUTH
if "%choice%"=="2" goto ADD_COPYRIGHT
if "%choice%"=="3" goto REMOVE_COPYRIGHT
if "%choice%"=="4" goto DEMO_ADD
if "%choice%"=="5" goto DEMO_REMOVE
if "%choice%"=="6" goto CLEANUP_BACKUPS
if "%choice%"=="7" goto SHOW_HELP
if "%choice%"=="8" goto EXIT
echo 无效选项，请重新选择
echo.
goto MENU

:SETUP_AUTH
echo.
echo 🔐 正在启动认证密钥设置工具...
echo.
node scripts/setup-auth.js
echo.
pause
goto MENU

:ADD_COPYRIGHT
echo.
echo 🚀 正在启动版权添加工具...
echo.
node scripts/add-copyright.js
echo.
pause
goto MENU

:REMOVE_COPYRIGHT
echo.
echo 🗑️  正在启动版权删除工具...
echo.
node scripts/remove-copyright.js
echo.
pause
goto MENU

:DEMO_ADD
echo.
echo 🧪 正在启动版权添加演示...
echo.
node scripts/demo-copyright.js
echo.
pause
goto MENU

:DEMO_REMOVE
echo.
echo 🧪 正在启动版权删除演示...
echo.
node scripts/demo-remove-copyright.js
echo.
pause
goto MENU



:CLEANUP_BACKUPS
echo.
echo 🧹 正在启动备份文件清理工具...
echo.
node scripts/cleanup-backups.js
echo.
pause
goto MENU

:SHOW_HELP
echo.
echo 📖 使用说明：
echo.
echo 1. 首次使用请先设置认证密钥（选项1）
echo 2. 添加版权：为所有代码文件添加版权信息（选项2）
echo 3. 删除版权：移除所有 Ace Platform 版权信息（选项3）
echo 4. 演示模式：安全测试添加/删除效果（选项4/5）
echo 5. 清理备份：删除所有版权工具创建的备份文件（选项6）
echo 7. 支持的文件类型：.js .ts .vue .css .scss .html 等
echo 8. 已有版权信息的文件会被自动跳过（添加模式）
echo 9. 删除时会自动创建备份文件
echo.
echo 详细说明请查看 scripts/README.md 文件
echo.
pause
goto MENU

:EXIT
echo.
echo 👋 感谢使用 Ace Platform 版权管理工具！
echo.
pause
exit
