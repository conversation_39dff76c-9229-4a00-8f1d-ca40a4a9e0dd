#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 演示用的简单版权模板
const DEMO_COPYRIGHT = `/**
 * Copyright (c) ${new Date().getFullYear()} Ace Platform系统
 * 
 * 本软件受版权保护，未经授权不得复制、修改或分发
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 * @created ${new Date().toISOString().split('T')[0]}
 */

`;

class DemoCopyrightManager {
  constructor() {
    this.testFiles = [
      'frontend/src/views/auth/Login.vue',
      'frontend/src/router/index.ts',
      'backend/src/index.ts'
    ];
  }

  // 检查文件是否已有版权信息
  hasExistingCopyright(content) {
    return content.includes('Copyright') && content.includes('Ace Platform');
  }

  // 为文件添加版权信息（演示模式）
  addCopyrightDemo(filePath) {
    try {
      if (!fs.existsSync(filePath)) {
        console.log(`⏭️  跳过 ${filePath} (文件不存在)`);
        return { success: true, skipped: true };
      }

      const content = fs.readFileSync(filePath, 'utf8');
      
      if (this.hasExistingCopyright(content)) {
        console.log(`⏭️  跳过 ${filePath} (已有版权信息)`);
        return { success: true, skipped: true };
      }

      // 创建备份
      const backupPath = filePath + '.backup';
      fs.writeFileSync(backupPath, content);

      // 添加版权信息
      const newContent = DEMO_COPYRIGHT + content;
      fs.writeFileSync(filePath, newContent, 'utf8');

      console.log(`✅ 已添加版权信息: ${filePath}`);
      console.log(`📁 备份文件: ${backupPath}`);
      return { success: true, skipped: false };

    } catch (error) {
      console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
      return { success: false, error: error.message };
    }
  }

  // 恢复文件（移除版权信息）
  restoreFile(filePath) {
    try {
      const backupPath = filePath + '.backup';
      
      if (!fs.existsSync(backupPath)) {
        console.log(`⏭️  跳过 ${filePath} (无备份文件)`);
        return false;
      }

      const backupContent = fs.readFileSync(backupPath, 'utf8');
      fs.writeFileSync(filePath, backupContent, 'utf8');
      fs.unlinkSync(backupPath); // 删除备份文件

      console.log(`🔄 已恢复文件: ${filePath}`);
      return true;

    } catch (error) {
      console.error(`❌ 恢复文件失败 ${filePath}:`, error.message);
      return false;
    }
  }

  // 演示添加版权
  async demoAdd() {
    console.log('🚀 Ace Platform 版权添加演示');
    console.log('=====================================');
    console.log('📁 演示文件列表:');
    this.testFiles.forEach((file, index) => {
      console.log(`  ${index + 1}. ${file}`);
    });
    console.log('');

    let processed = 0;
    let skipped = 0;

    for (const file of this.testFiles) {
      const result = this.addCopyrightDemo(file);
      
      if (result.success) {
        if (result.skipped) {
          skipped++;
        } else {
          processed++;
        }
      }
    }

    console.log('\n📈 演示结果统计:');
    console.log(`✅ 成功添加: ${processed} 个文件`);
    console.log(`⏭️  跳过文件: ${skipped} 个文件`);
    console.log('\n💡 提示: 运行 "node scripts/demo-copyright.js restore" 可以恢复文件');
  }

  // 演示恢复文件
  async demoRestore() {
    console.log('🔄 Ace Platform 文件恢复演示');
    console.log('=====================================');

    let restored = 0;

    for (const file of this.testFiles) {
      if (this.restoreFile(file)) {
        restored++;
      }
    }

    console.log(`\n📈 恢复结果: ${restored} 个文件已恢复`);
  }

  // 主执行函数
  async run() {
    const args = process.argv.slice(2);
    const command = args[0];

    if (command === 'restore') {
      await this.demoRestore();
    } else {
      await this.demoAdd();
    }
  }
}

// 主程序入口
if (require.main === module) {
  const manager = new DemoCopyrightManager();
  manager.run().catch(error => {
    console.error('💥 程序执行出错:', error);
    process.exit(1);
  });
}

module.exports = DemoCopyrightManager;
