#!/bin/bash

# 🔍 宝塔面板部署检查脚本
# 检查游戏代练管理系统部署状态

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
PROJECT_DIR="/www/wwwroot/game-boost"
DOMAIN=${1:-"your-domain.com"}

echo -e "${BLUE}🔍 游戏代练管理系统部署检查${NC}"
echo "=================================================="
echo ""

# 检查项目目录
echo -e "${BLUE}📁 检查项目目录...${NC}"
if [ -d "$PROJECT_DIR" ]; then
    echo -e "${GREEN}✅ 项目目录存在: $PROJECT_DIR${NC}"
    
    # 检查前端构建文件
    if [ -d "$PROJECT_DIR/frontend/dist" ]; then
        echo -e "${GREEN}✅ 前端构建文件存在${NC}"
    else
        echo -e "${RED}❌ 前端构建文件不存在${NC}"
    fi
    
    # 检查后端构建文件
    if [ -d "$PROJECT_DIR/backend/dist" ]; then
        echo -e "${GREEN}✅ 后端构建文件存在${NC}"
    else
        echo -e "${RED}❌ 后端构建文件不存在${NC}"
    fi
    
    # 检查环境配置文件
    if [ -f "$PROJECT_DIR/backend/.env" ]; then
        echo -e "${GREEN}✅ 后端环境配置文件存在${NC}"
    else
        echo -e "${RED}❌ 后端环境配置文件不存在${NC}"
    fi
    
else
    echo -e "${RED}❌ 项目目录不存在: $PROJECT_DIR${NC}"
fi

echo ""

# 检查服务状态
echo -e "${BLUE}🚀 检查服务状态...${NC}"

# 检查 Node.js
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    echo -e "${GREEN}✅ Node.js 已安装: $NODE_VERSION${NC}"
else
    echo -e "${RED}❌ Node.js 未安装${NC}"
fi

# 检查 PM2
if command -v pm2 &> /dev/null; then
    echo -e "${GREEN}✅ PM2 已安装${NC}"
    
    # 检查 PM2 进程
    if pm2 list | grep -q "game-boost-backend"; then
        PM2_STATUS=$(pm2 list | grep "game-boost-backend" | awk '{print $10}')
        if [ "$PM2_STATUS" = "online" ]; then
            echo -e "${GREEN}✅ 后端服务运行中${NC}"
        else
            echo -e "${RED}❌ 后端服务状态异常: $PM2_STATUS${NC}"
        fi
    else
        echo -e "${RED}❌ 后端服务未启动${NC}"
    fi
else
    echo -e "${RED}❌ PM2 未安装${NC}"
fi

# 检查 Nginx
if command -v nginx &> /dev/null; then
    echo -e "${GREEN}✅ Nginx 已安装${NC}"
    
    # 检查 Nginx 状态
    if systemctl is-active --quiet nginx; then
        echo -e "${GREEN}✅ Nginx 服务运行中${NC}"
    else
        echo -e "${RED}❌ Nginx 服务未运行${NC}"
    fi
    
    # 检查网站配置
    if [ -f "/www/server/panel/vhost/nginx/$DOMAIN.conf" ]; then
        echo -e "${GREEN}✅ Nginx 网站配置存在${NC}"
    else
        echo -e "${YELLOW}⚠️  Nginx 网站配置不存在: $DOMAIN.conf${NC}"
    fi
else
    echo -e "${RED}❌ Nginx 未安装${NC}"
fi

# 检查 MySQL
if command -v mysql &> /dev/null; then
    echo -e "${GREEN}✅ MySQL 已安装${NC}"
    
    # 检查 MySQL 状态
    if systemctl is-active --quiet mysql || systemctl is-active --quiet mysqld; then
        echo -e "${GREEN}✅ MySQL 服务运行中${NC}"
        
        # 检查数据库
        if mysql -e "USE game_boost_db;" 2>/dev/null; then
            echo -e "${GREEN}✅ 数据库 game_boost_db 存在${NC}"
        else
            echo -e "${RED}❌ 数据库 game_boost_db 不存在${NC}"
        fi
    else
        echo -e "${RED}❌ MySQL 服务未运行${NC}"
    fi
else
    echo -e "${RED}❌ MySQL 未安装${NC}"
fi

echo ""

# 检查端口占用
echo -e "${BLUE}🔌 检查端口占用...${NC}"

# 检查 3000 端口（后端）
if netstat -tlnp | grep -q ":3000 "; then
    echo -e "${GREEN}✅ 端口 3000 (后端) 已被占用${NC}"
else
    echo -e "${RED}❌ 端口 3000 (后端) 未被占用${NC}"
fi

# 检查 80 端口（HTTP）
if netstat -tlnp | grep -q ":80 "; then
    echo -e "${GREEN}✅ 端口 80 (HTTP) 已被占用${NC}"
else
    echo -e "${RED}❌ 端口 80 (HTTP) 未被占用${NC}"
fi

# 检查 443 端口（HTTPS）
if netstat -tlnp | grep -q ":443 "; then
    echo -e "${GREEN}✅ 端口 443 (HTTPS) 已被占用${NC}"
else
    echo -e "${YELLOW}⚠️  端口 443 (HTTPS) 未被占用 (SSL 未配置)${NC}"
fi

echo ""

# 检查日志文件
echo -e "${BLUE}📋 检查日志文件...${NC}"

if [ -d "$PROJECT_DIR/logs" ]; then
    echo -e "${GREEN}✅ 日志目录存在${NC}"
    
    # 检查错误日志
    if [ -f "$PROJECT_DIR/logs/err.log" ]; then
        ERROR_COUNT=$(wc -l < "$PROJECT_DIR/logs/err.log" 2>/dev/null || echo "0")
        if [ "$ERROR_COUNT" -gt 0 ]; then
            echo -e "${YELLOW}⚠️  发现 $ERROR_COUNT 行错误日志${NC}"
            echo "最近的错误:"
            tail -n 3 "$PROJECT_DIR/logs/err.log" | sed 's/^/    /'
        else
            echo -e "${GREEN}✅ 无错误日志${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  错误日志文件不存在${NC}"
    fi
else
    echo -e "${RED}❌ 日志目录不存在${NC}"
fi

echo ""

# 检查磁盘空间
echo -e "${BLUE}💾 检查磁盘空间...${NC}"
DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -lt 80 ]; then
    echo -e "${GREEN}✅ 磁盘空间充足 (已使用 $DISK_USAGE%)${NC}"
elif [ "$DISK_USAGE" -lt 90 ]; then
    echo -e "${YELLOW}⚠️  磁盘空间紧张 (已使用 $DISK_USAGE%)${NC}"
else
    echo -e "${RED}❌ 磁盘空间不足 (已使用 $DISK_USAGE%)${NC}"
fi

echo ""

# 检查内存使用
echo -e "${BLUE}🧠 检查内存使用...${NC}"
MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ "$MEMORY_USAGE" -lt 80 ]; then
    echo -e "${GREEN}✅ 内存使用正常 (已使用 $MEMORY_USAGE%)${NC}"
elif [ "$MEMORY_USAGE" -lt 90 ]; then
    echo -e "${YELLOW}⚠️  内存使用较高 (已使用 $MEMORY_USAGE%)${NC}"
else
    echo -e "${RED}❌ 内存使用过高 (已使用 $MEMORY_USAGE%)${NC}"
fi

echo ""

# 网络连接测试
echo -e "${BLUE}🌐 网络连接测试...${NC}"

# 测试后端 API (更新API路径)
API_ENDPOINTS=(
    "http://localhost:3000/api/v1/health"
    "http://localhost:3000/"
)

for endpoint in "${API_ENDPOINTS[@]}"; do
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$endpoint" 2>/dev/null || echo "000")
    if [ "$HTTP_CODE" = "200" ]; then
        echo -e "${GREEN}✅ API响应正常: $endpoint${NC}"
    else
        echo -e "${RED}❌ API无响应: $endpoint (HTTP: $HTTP_CODE)${NC}"
    fi
done

# 测试前端页面
if [ -f "$PROJECT_DIR/frontend/dist/index.html" ]; then
    echo -e "${GREEN}✅ 前端构建文件存在${NC}"

    # 测试通过Nginx访问
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost/" 2>/dev/null || echo "000")
    if [ "$HTTP_CODE" = "200" ]; then
        echo -e "${GREEN}✅ 前端页面响应正常${NC}"
    else
        echo -e "${RED}❌ 前端页面无响应 (HTTP: $HTTP_CODE)${NC}"
    fi
else
    echo -e "${RED}❌ 前端构建文件不存在${NC}"
fi

echo ""

# 总结
echo -e "${BLUE}📊 检查总结${NC}"
echo "=================================================="

# 计算检查项目
TOTAL_CHECKS=0
PASSED_CHECKS=0

# 这里可以根据上面的检查结果来统计
echo "请根据上述检查结果进行相应的修复操作"
echo ""

echo -e "${BLUE}🔧 常用修复命令:${NC}"
echo "重启后端服务: pm2 restart game-boost-backend"
echo "查看后端日志: pm2 logs game-boost-backend"
echo "重启 Nginx: systemctl restart nginx"
echo "重启 MySQL: systemctl restart mysql"
echo "查看系统资源: htop"
echo ""

echo -e "${BLUE}📚 更多帮助:${NC}"
echo "查看完整部署指南: cat docs/宝塔面板部署指南.md"
echo "运行部署助手: bash scripts/bt-deploy-helper.sh"
