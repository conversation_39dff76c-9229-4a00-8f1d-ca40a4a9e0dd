#!/usr/bin/env node

/**
 * 效率计算演示脚本
 * 模拟和验证员工绩效排行榜中效率计算的修复
 */

console.log('🧪 员工效率计算演示\n');

// 模拟原始数据（基于截图中的数据）
const testCases = [
  {
    name: '123 (Lv.1)',
    totalCommission: 100,
    originalHours: 0.03,  // 原始异常小的工时
    description: '原始数据 - 工时异常小'
  },
  {
    name: '员工A',
    totalCommission: 200,
    originalHours: 0.05,
    description: '类似异常情况'
  },
  {
    name: '员工B',
    totalCommission: 150,
    originalHours: 2.5,
    description: '正常工时数据'
  },
  {
    name: '员工C',
    totalCommission: 500,
    originalHours: 0.02,
    description: '高收益 + 极小工时'
  }
];

// 原始效率计算函数（修复前）
function calculateOriginalEfficiency(commission, hours) {
  return hours > 0 ? Math.round((commission / hours) * 100) / 100 : 0;
}

// 修复后的效率计算函数
function calculateFixedEfficiency(commission, hours) {
  // 1. 工时修正：最小工时设为0.1小时（6分钟）
  const correctedHours = Math.max(0.1, hours);
  
  // 2. 效率计算
  let efficiency = 0;
  if (correctedHours > 0 && commission > 0) {
    const rawEfficiency = commission / correctedHours;
    // 3. 设置效率上限为1000元/小时
    efficiency = Math.min(rawEfficiency, 1000);
    efficiency = Math.round(efficiency * 100) / 100;
  }
  
  return efficiency;
}

// 工时修正函数
function correctWorkHours(originalHours) {
  if (originalHours && originalHours > 0) {
    // 如果有实际工时且大于0，设置最小值为0.1小时
    return Math.max(0.1, originalHours);
  } else {
    // 如果没有工时记录，默认设置为1小时
    return 1.0;
  }
}

console.log('📊 效率计算对比分析:\n');

testCases.forEach((testCase, index) => {
  const { name, totalCommission, originalHours, description } = testCase;
  
  console.log(`${index + 1}. ${name} (${description})`);
  console.log(`   总收益: ¥${totalCommission}`);
  console.log(`   原始工时: ${originalHours}h`);
  
  // 原始计算
  const originalEfficiency = calculateOriginalEfficiency(totalCommission, originalHours);
  console.log(`   修复前效率: ¥${originalEfficiency}/h`);
  
  // 修复后计算
  const correctedHours = correctWorkHours(originalHours);
  const fixedEfficiency = calculateFixedEfficiency(totalCommission, correctedHours);
  console.log(`   修正工时: ${correctedHours}h`);
  console.log(`   修复后效率: ¥${fixedEfficiency}/h`);
  
  // 分析
  if (originalEfficiency > 1000) {
    console.log(`   ⚠️  原始效率异常高 (${originalEfficiency}元/h)`);
  }
  
  if (fixedEfficiency === 1000) {
    console.log(`   🔒 效率已限制在上限值`);
  }
  
  if (correctedHours !== originalHours) {
    console.log(`   🔧 工时已修正 (${originalHours}h → ${correctedHours}h)`);
  }
  
  console.log('');
});

console.log('🔍 修复说明:\n');
console.log('1. 工时修正:');
console.log('   - 最小工时设为0.1小时（6分钟），避免过小工时导致效率异常');
console.log('   - 无工时记录时默认设为1小时');
console.log('');
console.log('2. 效率限制:');
console.log('   - 设置效率上限为1000元/小时，避免显示异常高的数值');
console.log('   - 保持计算逻辑正确性的同时提升用户体验');
console.log('');
console.log('3. 显示优化:');
console.log('   - 添加警告图标提示效率达到上限');
console.log('   - 使用formatNumber格式化数值显示');
console.log('');

// 验证修复效果
console.log('✅ 修复验证:\n');

const problematicCase = testCases[0]; // 使用截图中的数据
const originalResult = calculateOriginalEfficiency(problematicCase.totalCommission, problematicCase.originalHours);
const fixedResult = calculateFixedEfficiency(problematicCase.totalCommission, correctWorkHours(problematicCase.originalHours));

console.log(`原问题: ¥${problematicCase.totalCommission} ÷ ${problematicCase.originalHours}h = ¥${originalResult}/h (异常高)`);
console.log(`修复后: ¥${problematicCase.totalCommission} ÷ ${correctWorkHours(problematicCase.originalHours)}h = ¥${fixedResult}/h (合理范围)`);
console.log('');
console.log('🎯 修复完成！效率计算现在更加合理和用户友好。');
