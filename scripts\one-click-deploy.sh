#!/bin/bash

# 🚀 王者荣耀代练管理系统 - 一键部署脚本
# 适用于宝塔面板环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
PROJECT_DIR="/www/wwwroot/game-boost"

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${PURPLE}[STEP]${NC} $1"; }

# 显示欢迎信息
show_welcome() {
    clear
    echo -e "${BLUE}"
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    🎮 王者荣耀代练管理系统 - 一键部署工具                    ║
║                                                              ║
║    🚀 自动化部署到宝塔面板环境                               ║
║    ⚡ 支持前后端分离架构                                     ║
║    🔧 自动配置数据库和环境                                   ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
    echo "部署模式选择:"
    echo "1. 🔍 环境检查模式 (检查部署环境)"
    echo "2. ⚙️  配置模式 (配置项目环境)"
    echo "3. 🚀 完整部署模式 (一键完整部署)"
    echo "4. 🔧 修复模式 (修复部署问题)"
    echo "5. 📊 状态检查 (检查部署状态)"
    echo "6. 🔴 配置Redis (可选缓存组件)"
    echo "7. 📧 配置邮件 (可选邮件服务)"
    echo ""
}

# 选择部署模式
select_deploy_mode() {
    while true; do
        read -p "请选择部署模式 (1-7): " mode
        case $mode in
            1)
                log_step "启动环境检查模式..."
                run_environment_check
                break
                ;;
            2)
                log_step "启动配置模式..."
                run_configuration_mode
                break
                ;;
            3)
                log_step "启动完整部署模式..."
                run_full_deployment
                break
                ;;
            4)
                log_step "启动修复模式..."
                run_repair_mode
                break
                ;;
            5)
                log_step "启动状态检查..."
                run_status_check
                break
                ;;
            6)
                log_step "启动Redis配置..."
                run_redis_setup
                break
                ;;
            7)
                log_step "启动邮件配置..."
                run_email_setup
                break
                ;;
            *)
                log_error "无效选择，请输入 1-7"
                ;;
        esac
    done
}

# 环境检查模式
run_environment_check() {
    log_info "运行环境检查..."
    
    if [ -f "$SCRIPT_DIR/bt-pre-deployment-check.sh" ]; then
        chmod +x "$SCRIPT_DIR/bt-pre-deployment-check.sh"
        bash "$SCRIPT_DIR/bt-pre-deployment-check.sh"
    else
        log_error "环境检查脚本不存在"
        exit 1
    fi
    
    echo ""
    read -p "按回车键继续..."
}

# 配置模式
run_configuration_mode() {
    log_info "运行项目配置..."
    
    # 检查项目是否已上传
    if [ ! -d "$PROJECT_DIR" ]; then
        log_error "项目目录不存在: $PROJECT_DIR"
        log_info "请先在宝塔面板创建网站，并上传项目文件"
        exit 1
    fi
    
    if [ -f "$SCRIPT_DIR/bt-project-config.sh" ]; then
        chmod +x "$SCRIPT_DIR/bt-project-config.sh"
        bash "$SCRIPT_DIR/bt-project-config.sh"
    else
        log_error "项目配置脚本不存在"
        exit 1
    fi
}

# 完整部署模式
run_full_deployment() {
    log_info "开始完整部署流程..."
    
    # 步骤1: 环境检查
    log_step "步骤 1/5: 环境检查"
    run_environment_check
    
    # 步骤2: 项目准备
    log_step "步骤 2/5: 项目准备"
    prepare_project
    
    # 步骤3: 配置项目
    log_step "步骤 3/5: 配置项目"
    run_configuration_mode
    
    # 步骤4: 启动服务
    log_step "步骤 4/5: 启动服务"
    start_services
    
    # 步骤5: 验证部署
    log_step "步骤 5/5: 验证部署"
    verify_deployment
    
    log_success "🎉 完整部署完成！"
}

# 修复模式
run_repair_mode() {
    log_info "启动修复模式..."
    
    echo "常见问题修复选项:"
    echo "1. 重启所有服务"
    echo "2. 重新构建项目"
    echo "3. 修复文件权限"
    echo "4. 重新生成配置文件"
    echo "5. 清理并重新安装依赖"
    echo ""
    
    read -p "请选择修复选项 (1-5): " repair_option
    
    case $repair_option in
        1) repair_restart_services ;;
        2) repair_rebuild_project ;;
        3) repair_file_permissions ;;
        4) repair_regenerate_configs ;;
        5) repair_reinstall_dependencies ;;
        *) log_error "无效选择" ;;
    esac
}

# 状态检查
run_status_check() {
    log_info "检查部署状态..."

    if [ -f "$SCRIPT_DIR/deployment-check.sh" ]; then
        chmod +x "$SCRIPT_DIR/deployment-check.sh"
        bash "$SCRIPT_DIR/deployment-check.sh"
    else
        log_error "状态检查脚本不存在"
        exit 1
    fi
}

# Redis配置
run_redis_setup() {
    log_info "配置Redis缓存服务..."

    if [ -f "$SCRIPT_DIR/setup-redis.sh" ]; then
        chmod +x "$SCRIPT_DIR/setup-redis.sh"
        bash "$SCRIPT_DIR/setup-redis.sh"
    else
        log_error "Redis配置脚本不存在"
        exit 1
    fi
}

# 邮件配置
run_email_setup() {
    log_info "配置邮件服务..."

    if [ -f "$SCRIPT_DIR/setup-email.sh" ]; then
        chmod +x "$SCRIPT_DIR/setup-email.sh"
        bash "$SCRIPT_DIR/setup-email.sh"
    else
        log_error "邮件配置脚本不存在"
        exit 1
    fi
}

# 项目准备
prepare_project() {
    log_info "准备项目环境..."

    # 检查项目目录
    if [ ! -d "$PROJECT_DIR" ]; then
        log_info "创建项目目录..."
        mkdir -p "$PROJECT_DIR"
        chown -R www:www "$PROJECT_DIR"
    fi

    # 如果当前目录有项目文件，复制到部署目录
    if [ -f "$PROJECT_ROOT/package.json" ] && [ "$PROJECT_ROOT" != "$PROJECT_DIR" ]; then
        log_info "复制项目文件到部署目录..."
        cp -r "$PROJECT_ROOT"/* "$PROJECT_DIR/"
        chown -R www:www "$PROJECT_DIR"
    fi

    # 检查ip2region文件
    if [ ! -d "$PROJECT_DIR/ip2region-master" ]; then
        log_error "ip2region-master目录不存在"
        log_info "请确保已上传完整的ip2region-master目录到项目根目录"
        return 1
    fi

    if [ ! -f "$PROJECT_DIR/ip2region-master/data/ip2region.xdb" ]; then
        log_error "ip2region数据库文件不存在"
        return 1
    fi

    log_success "项目准备完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    cd "$PROJECT_DIR"
    
    # 启动PM2服务
    if [ -f "ecosystem.config.js" ]; then
        log_info "启动PM2服务..."
        pm2 start ecosystem.config.js
        pm2 save
        log_success "PM2服务启动完成"
    else
        log_error "PM2配置文件不存在"
        return 1
    fi
    
    # 重载Nginx
    log_info "重载Nginx配置..."
    nginx -t && nginx -s reload
    log_success "Nginx配置重载完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署结果..."
    
    # 检查PM2进程
    if pm2 list | grep -q "game-boost-backend.*online"; then
        log_success "✅ 后端服务运行正常"
    else
        log_error "❌ 后端服务异常"
        return 1
    fi
    
    # 检查API响应
    sleep 3  # 等待服务启动
    if curl -s "http://localhost:3000/api/v1/health" | grep -q "ok\|success\|healthy"; then
        log_success "✅ API响应正常"
    else
        log_warning "⚠️  API响应异常，请检查日志"
    fi
    
    log_success "部署验证完成"
}

# 修复函数
repair_restart_services() {
    log_info "重启所有服务..."
    pm2 restart all
    systemctl restart nginx
    systemctl restart mysql
    log_success "服务重启完成"
}

repair_rebuild_project() {
    log_info "重新构建项目..."
    cd "$PROJECT_DIR/backend"
    npm run build
    cd "$PROJECT_DIR/frontend"
    npm run build
    log_success "项目重新构建完成"
}

repair_file_permissions() {
    log_info "修复文件权限..."
    chown -R www:www "$PROJECT_DIR"
    chmod -R 755 "$PROJECT_DIR"
    chmod 600 "$PROJECT_DIR/backend/.env" 2>/dev/null || true
    log_success "文件权限修复完成"
}

repair_regenerate_configs() {
    log_info "重新生成配置文件..."
    if [ -f "$SCRIPT_DIR/bt-project-config.sh" ]; then
        bash "$SCRIPT_DIR/bt-project-config.sh"
    fi
    log_success "配置文件重新生成完成"
}

repair_reinstall_dependencies() {
    log_info "清理并重新安装依赖..."
    
    # 后端依赖
    cd "$PROJECT_DIR/backend"
    rm -rf node_modules package-lock.json
    npm cache clean --force
    npm install
    
    # 前端依赖
    cd "$PROJECT_DIR/frontend"
    rm -rf node_modules package-lock.json
    npm cache clean --force
    npm install
    
    log_success "依赖重新安装完成"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -c, --check    运行环境检查"
    echo "  -d, --deploy   运行完整部署"
    echo "  -s, --status   检查部署状态"
    echo "  -r, --repair   运行修复模式"
    echo ""
    echo "示例:"
    echo "  $0              # 交互式模式"
    echo "  $0 --check     # 仅检查环境"
    echo "  $0 --deploy    # 完整部署"
    echo ""
}

# 主函数
main() {
    # 检查参数
    case "${1:-}" in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--check)
            run_environment_check
            exit 0
            ;;
        -d|--deploy)
            run_full_deployment
            exit 0
            ;;
        -s|--status)
            run_status_check
            exit 0
            ;;
        -r|--repair)
            run_repair_mode
            exit 0
            ;;
        "")
            # 交互式模式
            show_welcome
            select_deploy_mode
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
