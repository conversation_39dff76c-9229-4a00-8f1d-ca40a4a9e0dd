@echo off
echo 准备上传文件包...
echo.

set "UPLOAD_DIR=upload-package"

if exist "%UPLOAD_DIR%" (
    echo 清理旧文件...
    rmdir /s /q "%UPLOAD_DIR%"
)

mkdir "%UPLOAD_DIR%"

echo 复制必要文件...

echo   - 复制后端源码...
mkdir "%UPLOAD_DIR%\backend"
xcopy "backend\src" "%UPLOAD_DIR%\backend\src\" /e /i /q >nul 2>&1
xcopy "backend\prisma" "%UPLOAD_DIR%\backend\prisma\" /e /i /q >nul 2>&1
xcopy "backend\data" "%UPLOAD_DIR%\backend\data\" /e /i /q >nul 2>&1
xcopy "backend\lib" "%UPLOAD_DIR%\backend\lib\" /e /i /q >nul 2>&1
copy "backend\package.json" "%UPLOAD_DIR%\backend\" >nul 2>&1
copy "backend\package-lock.json" "%UPLOAD_DIR%\backend\" >nul 2>&1
copy "backend\tsconfig.json" "%UPLOAD_DIR%\backend\" >nul 2>&1
copy "backend\.env.example" "%UPLOAD_DIR%\backend\" >nul 2>&1

echo   - 复制前端源码...
mkdir "%UPLOAD_DIR%\frontend"
xcopy "frontend\src" "%UPLOAD_DIR%\frontend\src\" /e /i /q >nul 2>&1
xcopy "frontend\public" "%UPLOAD_DIR%\frontend\public\" /e /i /q >nul 2>&1
copy "frontend\package.json" "%UPLOAD_DIR%\frontend\" >nul 2>&1
copy "frontend\package-lock.json" "%UPLOAD_DIR%\frontend\" >nul 2>&1
copy "frontend\vite.config.ts" "%UPLOAD_DIR%\frontend\" >nul 2>&1
copy "frontend\tsconfig.json" "%UPLOAD_DIR%\frontend\" >nul 2>&1
copy "frontend\index.html" "%UPLOAD_DIR%\frontend\" >nul 2>&1

echo   - 复制部署脚本...
mkdir "%UPLOAD_DIR%\scripts"
copy "scripts\bt-deploy-helper.sh" "%UPLOAD_DIR%\scripts\" >nul 2>&1
copy "scripts\deployment-check.sh" "%UPLOAD_DIR%\scripts\" >nul 2>&1

echo   - 复制文档...
mkdir "%UPLOAD_DIR%\docs"
copy "docs\*.md" "%UPLOAD_DIR%\docs\" >nul 2>&1

echo   - 复制 ip2region 库...
xcopy "ip2region-master" "%UPLOAD_DIR%\ip2region-master\" /e /i /q >nul 2>&1

echo   - 复制数据库脚本...
mkdir "%UPLOAD_DIR%\database"
xcopy "database" "%UPLOAD_DIR%\database\" /e /i /q >nul 2>&1

copy "README.md" "%UPLOAD_DIR%\" >nul 2>&1
copy "package.json" "%UPLOAD_DIR%\" >nul 2>&1
copy "*.md" "%UPLOAD_DIR%\" >nul 2>&1

echo   - 创建 PM2 配置模板...
echo module.exports = { > "%UPLOAD_DIR%\ecosystem.config.js"
echo   apps: [{ >> "%UPLOAD_DIR%\ecosystem.config.js"
echo     name: 'game-boost-backend', >> "%UPLOAD_DIR%\ecosystem.config.js"
echo     script: './backend/dist/index.js', >> "%UPLOAD_DIR%\ecosystem.config.js"
echo     cwd: '/www/wwwroot/game-boost', >> "%UPLOAD_DIR%\ecosystem.config.js"
echo     instances: 1, >> "%UPLOAD_DIR%\ecosystem.config.js"
echo     exec_mode: 'fork', >> "%UPLOAD_DIR%\ecosystem.config.js"
echo     env: { >> "%UPLOAD_DIR%\ecosystem.config.js"
echo       NODE_ENV: 'production', >> "%UPLOAD_DIR%\ecosystem.config.js"
echo       PORT: 3000 >> "%UPLOAD_DIR%\ecosystem.config.js"
echo     }, >> "%UPLOAD_DIR%\ecosystem.config.js"
echo     error_file: './logs/err.log', >> "%UPLOAD_DIR%\ecosystem.config.js"
echo     out_file: './logs/out.log', >> "%UPLOAD_DIR%\ecosystem.config.js"
echo     log_file: './logs/combined.log', >> "%UPLOAD_DIR%\ecosystem.config.js"
echo     time: true, >> "%UPLOAD_DIR%\ecosystem.config.js"
echo     max_memory_restart: '1G' >> "%UPLOAD_DIR%\ecosystem.config.js"
echo   }] >> "%UPLOAD_DIR%\ecosystem.config.js"
echo } >> "%UPLOAD_DIR%\ecosystem.config.js"

echo   - 创建部署说明...
echo # 宝塔面板部署说明 > "%UPLOAD_DIR%\DEPLOY.md"
echo. >> "%UPLOAD_DIR%\DEPLOY.md"
echo ## 文件上传 >> "%UPLOAD_DIR%\DEPLOY.md"
echo. >> "%UPLOAD_DIR%\DEPLOY.md"
echo 1. 将文件上传到服务器 /www/wwwroot/game-boost/ >> "%UPLOAD_DIR%\DEPLOY.md"
echo 2. 解压文件 >> "%UPLOAD_DIR%\DEPLOY.md"
echo. >> "%UPLOAD_DIR%\DEPLOY.md"
echo ## 快速部署 >> "%UPLOAD_DIR%\DEPLOY.md"
echo. >> "%UPLOAD_DIR%\DEPLOY.md"
echo cd /www/wwwroot/game-boost >> "%UPLOAD_DIR%\DEPLOY.md"
echo chmod +x scripts/bt-deploy-helper.sh >> "%UPLOAD_DIR%\DEPLOY.md"
echo sudo bash scripts/bt-deploy-helper.sh >> "%UPLOAD_DIR%\DEPLOY.md"
echo. >> "%UPLOAD_DIR%\DEPLOY.md"
echo ## 部署前检查 >> "%UPLOAD_DIR%\DEPLOY.md"
echo. >> "%UPLOAD_DIR%\DEPLOY.md"
echo 确保宝塔面板已安装： >> "%UPLOAD_DIR%\DEPLOY.md"
echo - Node.js 18.x >> "%UPLOAD_DIR%\DEPLOY.md"
echo - MySQL 8.0+ >> "%UPLOAD_DIR%\DEPLOY.md"
echo - Nginx 1.18+ >> "%UPLOAD_DIR%\DEPLOY.md"
echo - PM2 管理器 >> "%UPLOAD_DIR%\DEPLOY.md"

echo 创建压缩包...
powershell -command "Compress-Archive -Path '%UPLOAD_DIR%\*' -DestinationPath 'game-boost-upload.zip' -Force" >nul 2>&1

if exist "game-boost-upload.zip" (
    echo 压缩包创建成功: game-boost-upload.zip
) else (
    echo 无法创建压缩包，请手动压缩 %UPLOAD_DIR% 文件夹
)

echo.
echo 准备完成！
echo.
echo 上传文件位置：
echo   - 目录: %UPLOAD_DIR%\
echo   - 压缩包: game-boost-upload.zip
echo.
echo 上传步骤：
echo 1. 在宝塔面板创建网站，根目录设为 /www/wwwroot/game-boost
echo 2. 上传 game-boost-upload.zip 到 /www/wwwroot/game-boost/
echo 3. 在宝塔文件管理器中解压文件
echo 4. 运行部署脚本: sudo bash scripts/bt-deploy-helper.sh
echo.
echo 详细说明请查看: %UPLOAD_DIR%\DEPLOY.md
echo.
pause
