# 📦 准备上传文件脚本 (PowerShell 版本)
# 创建用于上传到宝塔面板的文件包

param(
    [switch]$IncludeNodeModules = $false,
    [switch]$BuildFirst = $false
)

# 设置控制台编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "🚀 准备上传文件包..." -ForegroundColor Blue
Write-Host ""

# 配置变量
$UploadDir = "upload-package"
$ProjectRoot = Split-Path -Parent $PSScriptRoot
$ZipFile = "game-boost-upload.zip"

# 删除旧的上传目录
if (Test-Path $UploadDir) {
    Write-Host "🗑️ 清理旧文件..." -ForegroundColor Yellow
    Remove-Item -Path $UploadDir -Recurse -Force
}

# 创建上传目录
New-Item -ItemType Directory -Path $UploadDir -Force | Out-Null

Write-Host "📁 复制必要文件..." -ForegroundColor Green

# 复制后端文件
Write-Host "  - 复制后端源码..." -ForegroundColor Cyan
$BackendDir = Join-Path $UploadDir "backend"
New-Item -ItemType Directory -Path $BackendDir -Force | Out-Null

# 复制后端源码目录
$BackendSrc = Join-Path $ProjectRoot "backend\src"
if (Test-Path $BackendSrc) {
    Copy-Item -Path $BackendSrc -Destination $BackendDir -Recurse -Force
}

# 复制 Prisma 目录
$PrismaDir = Join-Path $ProjectRoot "backend\prisma"
if (Test-Path $PrismaDir) {
    Copy-Item -Path $PrismaDir -Destination $BackendDir -Recurse -Force
}

# 复制后端配置文件
$BackendFiles = @("package.json", "package-lock.json", "tsconfig.json", ".env.example")
foreach ($file in $BackendFiles) {
    $srcFile = Join-Path $ProjectRoot "backend\$file"
    if (Test-Path $srcFile) {
        Copy-Item -Path $srcFile -Destination $BackendDir -Force
    }
}

# 如果需要，复制 node_modules
if ($IncludeNodeModules) {
    $BackendNodeModules = Join-Path $ProjectRoot "backend\node_modules"
    if (Test-Path $BackendNodeModules) {
        Write-Host "  - 复制后端 node_modules..." -ForegroundColor Cyan
        Copy-Item -Path $BackendNodeModules -Destination $BackendDir -Recurse -Force
    }
}

# 复制前端文件
Write-Host "  - 复制前端源码..." -ForegroundColor Cyan
$FrontendDir = Join-Path $UploadDir "frontend"
New-Item -ItemType Directory -Path $FrontendDir -Force | Out-Null

# 复制前端源码目录
$FrontendSrc = Join-Path $ProjectRoot "frontend\src"
if (Test-Path $FrontendSrc) {
    Copy-Item -Path $FrontendSrc -Destination $FrontendDir -Recurse -Force
}

# 复制 public 目录
$PublicDir = Join-Path $ProjectRoot "frontend\public"
if (Test-Path $PublicDir) {
    Copy-Item -Path $PublicDir -Destination $FrontendDir -Recurse -Force
}

# 复制前端配置文件
$FrontendFiles = @("package.json", "package-lock.json", "vite.config.ts", "tsconfig.json", "index.html")
foreach ($file in $FrontendFiles) {
    $srcFile = Join-Path $ProjectRoot "frontend\$file"
    if (Test-Path $srcFile) {
        Copy-Item -Path $srcFile -Destination $FrontendDir -Force
    }
}

# 如果需要，复制前端 node_modules
if ($IncludeNodeModules) {
    $FrontendNodeModules = Join-Path $ProjectRoot "frontend\node_modules"
    if (Test-Path $FrontendNodeModules) {
        Write-Host "  - 复制前端 node_modules..." -ForegroundColor Cyan
        Copy-Item -Path $FrontendNodeModules -Destination $FrontendDir -Recurse -Force
    }
}

# 如果需要，复制构建后的文件
if ($BuildFirst) {
    Write-Host "  - 复制构建文件..." -ForegroundColor Cyan
    
    # 复制后端构建文件
    $BackendDist = Join-Path $ProjectRoot "backend\dist"
    if (Test-Path $BackendDist) {
        Copy-Item -Path $BackendDist -Destination $BackendDir -Recurse -Force
    }
    
    # 复制前端构建文件
    $FrontendDist = Join-Path $ProjectRoot "frontend\dist"
    if (Test-Path $FrontendDist) {
        Copy-Item -Path $FrontendDist -Destination $FrontendDir -Recurse -Force
    }
}

# 复制脚本文件
Write-Host "  - 复制部署脚本..." -ForegroundColor Cyan
$ScriptsDir = Join-Path $UploadDir "scripts"
New-Item -ItemType Directory -Path $ScriptsDir -Force | Out-Null

$ScriptFiles = @("bt-deploy-helper.sh", "deployment-check.sh")
foreach ($file in $ScriptFiles) {
    $srcFile = Join-Path $ProjectRoot "scripts\$file"
    if (Test-Path $srcFile) {
        Copy-Item -Path $srcFile -Destination $ScriptsDir -Force
    }
}

# 复制文档
Write-Host "  - 复制文档..." -ForegroundColor Cyan
$DocsDir = Join-Path $UploadDir "docs"
New-Item -ItemType Directory -Path $DocsDir -Force | Out-Null

$DocFiles = @("宝塔面板部署指南.md", "宝塔面板故障排除指南.md")
foreach ($file in $DocFiles) {
    $srcFile = Join-Path $ProjectRoot "docs\$file"
    if (Test-Path $srcFile) {
        Copy-Item -Path $srcFile -Destination $DocsDir -Force
    }
}

# 复制根目录文件
$RootFiles = @("README.md", "宝塔面板部署清单.md")
foreach ($file in $RootFiles) {
    $srcFile = Join-Path $ProjectRoot $file
    if (Test-Path $srcFile) {
        Copy-Item -Path $srcFile -Destination $UploadDir -Force
    }
}

# 创建 ecosystem.config.js 模板
Write-Host "  - 创建 PM2 配置模板..." -ForegroundColor Cyan
$EcosystemConfig = @"
module.exports = {
  apps: [{
    name: 'game-boost-backend',
    script: './backend/dist/index.js',
    cwd: '/www/wwwroot/game-boost',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    restart_delay: 4000
  }]
}
"@

$EcosystemConfig | Out-File -FilePath (Join-Path $UploadDir "ecosystem.config.js") -Encoding UTF8

# 创建部署说明文件
Write-Host "  - 创建部署说明..." -ForegroundColor Cyan
$DeployMd = @'
# 🚀 宝塔面板部署说明

## 📦 文件上传

1. 将整个文件夹内容上传到服务器的 `/www/wwwroot/game-boost/`
2. 或者使用压缩包上传并解压

## 🔧 快速部署

### 方法一：自动部署（推荐）
```bash
cd /www/wwwroot/game-boost
chmod +x scripts/bt-deploy-helper.sh
sudo bash scripts/bt-deploy-helper.sh
```

### 方法二：手动部署
按照 `docs/宝塔面板部署指南.md` 中的步骤操作

## 📋 部署前检查

确保宝塔面板已安装以下软件：
- Node.js (16.x 或 18.x)
- MySQL (8.0+)
- Nginx (1.18+)
- PM2 管理器

## 🔍 部署后检查

```bash
bash scripts/deployment-check.sh your-domain.com
```

## 📞 获取帮助

如遇问题，请查看：
- `docs/宝塔面板故障排除指南.md`
- 运行检查脚本获取诊断信息

## 📋 文件清单

- `backend/` - 后端源码
- `frontend/` - 前端源码
- `scripts/` - 部署脚本
- `docs/` - 部署文档
- `ecosystem.config.js` - PM2 配置
- `DEPLOY.md` - 本说明文件
'@

$DeployMd | Out-File -FilePath (Join-Path $UploadDir "DEPLOY.md") -Encoding UTF8

# 创建压缩包
Write-Host "📦 创建压缩包..." -ForegroundColor Green

try {
    # 删除旧的压缩包
    if (Test-Path $ZipFile) {
        Remove-Item $ZipFile -Force
    }
    
    # 创建新的压缩包
    Compress-Archive -Path "$UploadDir\*" -DestinationPath $ZipFile -Force
    Write-Host "✅ 压缩包创建成功: $ZipFile" -ForegroundColor Green
} catch {
    Write-Host "⚠️ 无法创建压缩包: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "请手动压缩 $UploadDir 文件夹" -ForegroundColor Yellow
}

# 计算文件大小
$UploadDirSize = (Get-ChildItem -Path $UploadDir -Recurse | Measure-Object -Property Length -Sum).Sum
$UploadDirSizeMB = [math]::Round($UploadDirSize / 1MB, 2)

if (Test-Path $ZipFile) {
    $ZipSize = (Get-Item $ZipFile).Length
    $ZipSizeMB = [math]::Round($ZipSize / 1MB, 2)
}

Write-Host ""
Write-Host "✅ 准备完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📁 上传文件位置：" -ForegroundColor Blue
Write-Host "  - 目录: $UploadDir\ ($UploadDirSizeMB MB)" -ForegroundColor White
if (Test-Path $ZipFile) {
    Write-Host "  - 压缩包: $ZipFile ($ZipSizeMB MB)" -ForegroundColor White
}
Write-Host ""
Write-Host "📋 上传步骤：" -ForegroundColor Blue
Write-Host "1. 在宝塔面板创建网站，根目录设为 /www/wwwroot/game-boost" -ForegroundColor White
Write-Host "2. 上传 $ZipFile 到 /www/wwwroot/game-boost/" -ForegroundColor White
Write-Host "3. 在宝塔文件管理器中解压文件" -ForegroundColor White
Write-Host "4. 运行部署脚本: sudo bash scripts/bt-deploy-helper.sh" -ForegroundColor White
Write-Host ""
Write-Host "🔗 详细说明请查看: $UploadDir\DEPLOY.md" -ForegroundColor Blue

# 显示参数说明
Write-Host ""
Write-Host "💡 脚本参数说明：" -ForegroundColor Yellow
Write-Host "  -IncludeNodeModules  包含 node_modules 目录（文件会很大）" -ForegroundColor White
Write-Host "  -BuildFirst          包含构建后的文件" -ForegroundColor White
Write-Host ""
Write-Host "示例: .\scripts\prepare-upload.ps1 -BuildFirst" -ForegroundColor White
