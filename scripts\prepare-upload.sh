#!/bin/bash

# 📦 准备上传文件脚本
# 创建用于上传到宝塔面板的文件包

echo "🚀 准备上传文件包..."

# 创建上传目录
UPLOAD_DIR="upload-package"
rm -rf $UPLOAD_DIR
mkdir -p $UPLOAD_DIR

echo "📁 复制必要文件..."

# 复制后端文件（排除不必要的文件）
echo "  - 复制后端源码..."
mkdir -p $UPLOAD_DIR/backend
cp -r backend/src $UPLOAD_DIR/backend/
cp -r backend/prisma $UPLOAD_DIR/backend/
cp backend/package.json $UPLOAD_DIR/backend/
cp backend/package-lock.json $UPLOAD_DIR/backend/ 2>/dev/null || echo "    package-lock.json 不存在，跳过"
cp backend/tsconfig.json $UPLOAD_DIR/backend/
cp backend/.env.example $UPLOAD_DIR/backend/

# 复制前端文件
echo "  - 复制前端源码..."
mkdir -p $UPLOAD_DIR/frontend
cp -r frontend/src $UPLOAD_DIR/frontend/
cp -r frontend/public $UPLOAD_DIR/frontend/
cp frontend/package.json $UPLOAD_DIR/frontend/
cp frontend/package-lock.json $UPLOAD_DIR/frontend/ 2>/dev/null || echo "    package-lock.json 不存在，跳过"
cp frontend/vite.config.ts $UPLOAD_DIR/frontend/
cp frontend/tsconfig.json $UPLOAD_DIR/frontend/
cp frontend/index.html $UPLOAD_DIR/frontend/

# 复制脚本文件
echo "  - 复制部署脚本..."
mkdir -p $UPLOAD_DIR/scripts
cp scripts/bt-deploy-helper.sh $UPLOAD_DIR/scripts/
cp scripts/deployment-check.sh $UPLOAD_DIR/scripts/
chmod +x $UPLOAD_DIR/scripts/*.sh

# 复制文档
echo "  - 复制文档..."
mkdir -p $UPLOAD_DIR/docs
cp docs/宝塔面板部署指南.md $UPLOAD_DIR/docs/ 2>/dev/null || echo "    部署指南不存在，跳过"
cp docs/宝塔面板故障排除指南.md $UPLOAD_DIR/docs/ 2>/dev/null || echo "    故障排除指南不存在，跳过"

# 复制根目录文件
cp README.md $UPLOAD_DIR/ 2>/dev/null || echo "  - README.md 不存在，跳过"

# 创建 ecosystem.config.js 模板
echo "  - 创建 PM2 配置模板..."
cat > $UPLOAD_DIR/ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'game-boost-backend',
    script: './backend/dist/index.js',
    cwd: '/www/wwwroot/game-boost',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    restart_delay: 4000
  }]
}
EOF

# 创建部署说明文件
echo "  - 创建部署说明..."
cat > $UPLOAD_DIR/DEPLOY.md << 'EOF'
# 🚀 宝塔面板部署说明

## 📦 文件上传

1. 将整个 upload-package 目录上传到服务器的 `/www/wwwroot/game-boost/`
2. 或者压缩后上传并解压

## 🔧 快速部署

### 方法一：自动部署（推荐）
```bash
cd /www/wwwroot/game-boost
chmod +x scripts/bt-deploy-helper.sh
sudo bash scripts/bt-deploy-helper.sh
```

### 方法二：手动部署
按照 `docs/宝塔面板部署指南.md` 中的步骤操作

## 📋 部署前检查

确保宝塔面板已安装以下软件：
- Node.js (16.x 或 18.x)
- MySQL (8.0+)
- Nginx (1.18+)
- PM2 管理器

## 🔍 部署后检查

```bash
bash scripts/deployment-check.sh your-domain.com
```

## 📞 获取帮助

如遇问题，请查看：
- `docs/宝塔面板故障排除指南.md`
- 运行检查脚本获取诊断信息
EOF

# 创建压缩包
echo "📦 创建压缩包..."
tar -czf game-boost-upload.tar.gz -C $UPLOAD_DIR .

echo "✅ 准备完成！"
echo ""
echo "📁 上传文件位置："
echo "  - 目录: $UPLOAD_DIR/"
echo "  - 压缩包: game-boost-upload.tar.gz"
echo ""
echo "📋 上传步骤："
echo "1. 在宝塔面板创建网站，根目录设为 /www/wwwroot/game-boost"
echo "2. 上传 game-boost-upload.tar.gz 到 /www/wwwroot/game-boost/"
echo "3. 在宝塔文件管理器中解压文件"
echo "4. 运行部署脚本: sudo bash scripts/bt-deploy-helper.sh"
echo ""
echo "🔗 详细说明请查看: $UPLOAD_DIR/DEPLOY.md"
