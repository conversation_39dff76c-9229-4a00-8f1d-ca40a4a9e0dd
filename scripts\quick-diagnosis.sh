#!/bin/bash

# 🔍 快速诊断脚本 - 检查当前部署状态

echo "🔍 快速诊断当前部署状态..."
echo "=================================="

# 检查项目目录
echo "📁 检查项目目录..."
if [ -d "/www/wwwroot/game-boost" ]; then
    echo "✅ 项目目录存在"
    ls -la /www/wwwroot/game-boost/
else
    echo "❌ 项目目录不存在"
fi

echo ""

# 检查服务状态
echo "🚀 检查服务状态..."
echo "PM2 进程列表:"
pm2 list

echo ""
echo "MySQL 服务状态:"
systemctl status mysql --no-pager -l

echo ""
echo "Nginx 服务状态:"
systemctl status nginx --no-pager -l

echo ""

# 检查端口占用
echo "🔌 检查端口占用..."
echo "端口 3000 (后端):"
netstat -tlnp | grep :3000 || echo "端口 3000 未被占用"

echo ""
echo "端口 80 (HTTP):"
netstat -tlnp | grep :80 || echo "端口 80 未被占用"

echo ""

# 检查日志
echo "📋 检查最近的错误日志..."
if [ -f "/www/wwwroot/game-boost/logs/err.log" ]; then
    echo "最近的错误日志:"
    tail -n 10 /www/wwwroot/game-boost/logs/err.log
else
    echo "错误日志文件不存在"
fi

echo ""

# 检查环境配置
echo "⚙️ 检查环境配置..."
if [ -f "/www/wwwroot/game-boost/backend/.env" ]; then
    echo "后端环境配置文件存在"
    echo "数据库配置:"
    grep -E "DATABASE_URL|JWT_SECRET|PORT" /www/wwwroot/game-boost/backend/.env || echo "配置文件为空或格式错误"
else
    echo "❌ 后端环境配置文件不存在"
fi

echo ""
echo "🔧 建议的修复步骤:"
echo "1. 如果项目目录不存在，重新运行部署脚本"
echo "2. 如果服务未启动，检查配置文件和依赖"
echo "3. 如果端口被占用，检查冲突进程"
echo "4. 查看详细错误日志进行针对性修复"
