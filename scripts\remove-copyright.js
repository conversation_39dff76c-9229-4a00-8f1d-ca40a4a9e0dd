#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const readline = require('readline');

// 认证配置 - 与添加脚本使用相同的认证系统
const AUTH_CONFIG = {
  CONFIG_PATH: path.join(__dirname, 'auth-config.json'),
  MAX_ATTEMPTS: 3,
  LOCKOUT_TIME: 300000 // 5分钟锁定时间
};

// 版权信息识别模式
const COPYRIGHT_PATTERNS = [
  // JavaScript/TypeScript 风格的版权注释
  {
    type: 'js',
    startPattern: /^\/\*\*?\s*\n?\s*\*?\s*Copyright.*Ace Platform/im,
    endPattern: /\*\/\s*\n*/,
    multiLine: true
  },
  
  // HTML/Vue 风格的版权注释
  {
    type: 'html',
    startPattern: /^<!--\s*\n?\s*Copyright.*Ace Platform/im,
    endPattern: /-->\s*\n*/,
    multiLine: true
  },
  
  // CSS 风格的版权注释
  {
    type: 'css',
    startPattern: /^\/\*\s*\n?\s*\*?\s*Copyright.*Ace Platform/im,
    endPattern: /\*\/\s*\n*/,
    multiLine: true
  }
];

// 需要排除的目录和文件
const EXCLUDE_PATTERNS = [
  'node_modules',
  '.git',
  'dist',
  'build',
  '.next',
  '.nuxt',
  'coverage',
  '.nyc_output',
  'logs',
  '*.log',
  '.env*',
  'package-lock.json',
  'yarn.lock',
  'pnpm-lock.yaml'
];

class CopyrightRemover {
  constructor() {
    this.failedAttempts = 0;
    this.lastAttemptTime = 0;
    this.isLocked = false;
  }

  // 读取认证配置
  getAuthConfig() {
    try {
      if (fs.existsSync(AUTH_CONFIG.CONFIG_PATH)) {
        const config = JSON.parse(fs.readFileSync(AUTH_CONFIG.CONFIG_PATH, 'utf8'));
        return config.keyHash;
      }
    } catch (error) {
      console.error('❌ 读取认证配置失败:', error.message);
    }
    return null;
  }

  // 认证功能
  async authenticate() {
    const storedHash = this.getAuthConfig();
    
    if (!storedHash) {
      console.log('❌ 未找到认证配置，请先运行: npm run copyright:setup');
      return false;
    }

    if (this.isLocked) {
      const timeLeft = Math.ceil((AUTH_CONFIG.LOCKOUT_TIME - (Date.now() - this.lastAttemptTime)) / 1000);
      console.log(`🔒 认证已锁定，请等待 ${timeLeft} 秒后重试`);
      return false;
    }

    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    return new Promise((resolve) => {
      rl.question('🔐 请输入认证密钥: ', (input) => {
        rl.close();
        
        const inputHash = crypto.createHash('sha256').update(input.trim()).digest('hex');
        
        if (inputHash === storedHash) {
          console.log('✅ 认证成功！');
          this.failedAttempts = 0;
          resolve(true);
        } else {
          this.failedAttempts++;
          this.lastAttemptTime = Date.now();
          
          console.log(`❌ 认证失败！剩余尝试次数: ${AUTH_CONFIG.MAX_ATTEMPTS - this.failedAttempts}`);
          
          if (this.failedAttempts >= AUTH_CONFIG.MAX_ATTEMPTS) {
            this.isLocked = true;
            console.log(`🔒 认证失败次数过多，已锁定 ${AUTH_CONFIG.LOCKOUT_TIME / 1000} 秒`);
          }
          
          resolve(false);
        }
      });
    });
  }

  // 检测并移除版权信息
  removeCopyrightFromContent(content, filePath) {
    let modifiedContent = content;
    let removed = false;

    // 检查是否包含 Ace Platform 版权信息
    if ((content.includes('Copyright') || content.includes('版权')) && content.includes('Ace Platform')) {
      // 查找版权块的开始和结束
      const lines = content.split('\n');
      let startIndex = -1;
      let endIndex = -1;

      // 查找版权块的开始
      for (let i = 0; i < lines.length; i++) {
        if ((lines[i].includes('Copyright') || lines[i].includes('版权')) && lines[i].includes('Ace Platform')) {
          // 向前查找注释开始
          for (let j = i; j >= 0; j--) {
            if (lines[j].includes('<!--') || lines[j].includes('/**') || lines[j].includes('/*')) {
              startIndex = j;
              break;
            }
          }

          // 向后查找注释结束
          for (let k = i; k < lines.length; k++) {
            if (lines[k].includes('-->') || lines[k].includes('*/')) {
              endIndex = k;
              break;
            }
          }
          break;
        }
      }

      // 如果找到了版权块，则移除它
      if (startIndex !== -1 && endIndex !== -1) {
        // 移除版权块（包括可能的空行）
        const beforeCopyright = lines.slice(0, startIndex);
        const afterCopyright = lines.slice(endIndex + 1);

        // 移除版权块后的空行
        while (afterCopyright.length > 0 && afterCopyright[0].trim() === '') {
          afterCopyright.shift();
        }

        modifiedContent = beforeCopyright.concat(afterCopyright).join('\n');
        removed = true;
        console.log(`🔍 在 ${filePath} 中发现并移除版权信息 (行 ${startIndex + 1}-${endIndex + 1})`);
      }
    }

    return { content: modifiedContent, removed };
  }

  // 检查文件是否应该被排除
  shouldExcludeFile(filePath) {
    const relativePath = path.relative(process.cwd(), filePath);
    
    return EXCLUDE_PATTERNS.some(pattern => {
      if (pattern.includes('*')) {
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        return regex.test(relativePath);
      }
      return relativePath.includes(pattern);
    });
  }

  // 递归获取所有文件
  getAllFiles(dirPath, fileList = []) {
    const files = fs.readdirSync(dirPath);
    
    files.forEach(file => {
      const filePath = path.join(dirPath, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        if (!this.shouldExcludeFile(filePath)) {
          this.getAllFiles(filePath, fileList);
        }
      } else {
        if (!this.shouldExcludeFile(filePath)) {
          fileList.push(filePath);
        }
      }
    });
    
    return fileList;
  }

  // 为单个文件移除版权信息
  removeCopyrightFromFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 检查是否包含 Ace Platform 版权信息
      if (!content.includes('Ace Platform')) {
        return { success: true, skipped: true, reason: '无版权信息' };
      }

      const result = this.removeCopyrightFromContent(content, filePath);
      
      if (!result.removed) {
        return { success: true, skipped: true, reason: '未识别到版权格式' };
      }

      // 创建备份
      const backupPath = filePath + '.backup-' + Date.now();
      fs.writeFileSync(backupPath, content, 'utf8');

      // 写入移除版权后的内容
      fs.writeFileSync(filePath, result.content, 'utf8');
      
      console.log(`✅ 已移除版权信息: ${filePath}`);
      console.log(`📁 备份文件: ${backupPath}`);
      return { success: true, skipped: false, backup: backupPath };
      
    } catch (error) {
      console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
      return { success: false, error: error.message };
    }
  }

  // 主执行函数
  async run() {
    console.log('🗑️  Ace Platform 版权删除工具启动');
    console.log('=====================================');
    console.log('⚠️  警告: 此操作将删除所有 Ace Platform 版权信息');
    console.log('💡 建议: 删除前请确保已备份重要文件');
    console.log('');
    
    // 认证检查
    const isAuthenticated = await this.authenticate();
    if (!isAuthenticated) {
      console.log('❌ 认证失败，程序退出');
      process.exit(1);
    }

    // 二次确认
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const confirmed = await new Promise((resolve) => {
      rl.question('⚠️  确认要删除所有版权信息吗？(输入 "YES" 确认): ', (answer) => {
        rl.close();
        resolve(answer.trim() === 'YES');
      });
    });

    if (!confirmed) {
      console.log('❌ 操作已取消');
      return;
    }
    
    console.log('\n📁 开始扫描文件...');
    
    // 获取所有文件
    const allFiles = this.getAllFiles(process.cwd());
    
    console.log(`📊 扫描完成: 找到 ${allFiles.length} 个文件`);
    
    if (allFiles.length === 0) {
      console.log('ℹ️  没有找到需要处理的文件');
      return;
    }
    
    // 处理文件
    console.log('\n🔄 开始删除版权信息...');
    
    let processed = 0;
    let skipped = 0;
    let failed = 0;
    const backupFiles = [];
    
    for (const file of allFiles) {
      const result = this.removeCopyrightFromFile(file);
      
      if (result.success) {
        if (result.skipped) {
          skipped++;
        } else {
          processed++;
          if (result.backup) {
            backupFiles.push(result.backup);
          }
        }
      } else {
        failed++;
      }
    }
    
    // 输出统计结果
    console.log('\n📈 处理完成统计:');
    console.log(`✅ 成功删除: ${processed} 个文件`);
    console.log(`⏭️  跳过文件: ${skipped} 个文件`);
    console.log(`❌ 失败文件: ${failed} 个文件`);
    console.log(`📁 总计文件: ${allFiles.length} 个文件`);
    
    if (backupFiles.length > 0) {
      console.log(`\n📦 备份文件列表 (${backupFiles.length} 个):`);
      backupFiles.forEach(backup => {
        console.log(`  📄 ${backup}`);
      });
      console.log('\n💡 如需恢复，可以手动重命名备份文件');
    }
    
    console.log('\n🎉 版权删除任务完成！');
  }
}

// 主程序入口
if (require.main === module) {
  const remover = new CopyrightRemover();
  remover.run().catch(error => {
    console.error('💥 程序执行出错:', error);
    process.exit(1);
  });
}

module.exports = CopyrightRemover;
