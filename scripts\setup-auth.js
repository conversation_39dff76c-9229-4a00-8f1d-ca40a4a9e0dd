#!/usr/bin/env node

/**
 * 认证密钥设置工具 - Ace Platform系统
 * 用于生成和管理版权脚本的认证密钥
 */

const crypto = require('crypto');
const readline = require('readline');
const fs = require('fs');
const path = require('path');

class AuthSetup {
  constructor() {
    this.configPath = path.join(__dirname, 'auth-config.json');
  }

  // 生成密钥哈希
  generateKeyHash(password) {
    return crypto.createHash('sha256').update(password).digest('hex');
  }

  // 设置新的认证密钥
  async setupNewKey() {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    console.log('🔐 Ace Platform 认证密钥设置工具');
    console.log('=====================================');
    console.log('请设置一个安全的认证密钥，用于保护版权添加脚本');
    console.log('');

    return new Promise((resolve) => {
      rl.question('请输入新的认证密钥 (至少8位): ', (password) => {
        if (password.length < 8) {
          console.log('❌ 密钥长度至少需要8位字符');
          rl.close();
          resolve(false);
          return;
        }

        rl.question('请再次确认密钥: ', (confirmPassword) => {
          rl.close();

          if (password !== confirmPassword) {
            console.log('❌ 两次输入的密钥不一致');
            resolve(false);
            return;
          }

          const keyHash = this.generateKeyHash(password);
          
          // 保存配置
          const config = {
            keyHash: keyHash,
            createdAt: new Date().toISOString(),
            description: 'Ace Platform 版权脚本认证密钥'
          };

          try {
            fs.writeFileSync(this.configPath, JSON.stringify(config, null, 2));
            console.log('✅ 认证密钥设置成功！');
            console.log(`📁 配置文件已保存到: ${this.configPath}`);
            console.log('');
            console.log('🔒 请妥善保管您的密钥，遗失后需要重新设置');
            console.log('💡 现在可以运行版权添加脚本: node scripts/add-copyright.js');
            resolve(true);
          } catch (error) {
            console.error('❌ 保存配置文件失败:', error.message);
            resolve(false);
          }
        });
      });
    });
  }

  // 更新版权脚本中的密钥哈希
  updateCopyrightScript(keyHash) {
    const scriptPath = path.join(__dirname, 'add-copyright.js');
    
    try {
      let content = fs.readFileSync(scriptPath, 'utf8');
      
      // 替换密钥哈希
      const hashRegex = /MASTER_KEY_HASH:\s*['"`]([^'"`]+)['"`]/;
      content = content.replace(hashRegex, `MASTER_KEY_HASH: '${keyHash}'`);
      
      fs.writeFileSync(scriptPath, content);
      console.log('✅ 版权脚本已更新新的认证密钥');
      
    } catch (error) {
      console.error('❌ 更新版权脚本失败:', error.message);
    }
  }

  // 主执行函数
  async run() {
    const success = await this.setupNewKey();
    
    if (success) {
      // 读取配置并更新脚本
      try {
        const config = JSON.parse(fs.readFileSync(this.configPath, 'utf8'));
        this.updateCopyrightScript(config.keyHash);
      } catch (error) {
        console.error('❌ 读取配置文件失败:', error.message);
      }
    }
  }
}

// 主程序入口
if (require.main === module) {
  const setup = new AuthSetup();
  setup.run().catch(error => {
    console.error('💥 程序执行出错:', error);
    process.exit(1);
  });
}

module.exports = AuthSetup;
