#!/bin/bash

# 📧 邮件服务配置脚本
# 王者荣耀代练管理系统 - 邮件通知功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 项目配置
PROJECT_DIR="/www/wwwroot/game-boost"

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 显示邮件服务信息
show_email_info() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "📧 邮件服务配置工具"
    echo "=================================================="
    echo -e "${NC}"
    echo "邮件功能:"
    echo "  ✅ 任务完成通知"
    echo "  ✅ 系统状态报告"
    echo "  ✅ 用户注册验证"
    echo "  ✅ 密码重置邮件"
    echo ""
    echo "支持的邮件服务商:"
    echo "  📮 QQ邮箱 (smtp.qq.com:587)"
    echo "  📮 163邮箱 (smtp.163.com:587)"
    echo "  📮 Gmail (smtp.gmail.com:587)"
    echo "  📮 企业邮箱 (自定义SMTP)"
    echo ""
    echo "注意: 邮件服务是可选功能，不配置不影响基本使用"
    echo ""
}

# 选择邮件服务商
select_email_provider() {
    echo "请选择邮件服务商:"
    echo "1. QQ邮箱"
    echo "2. 163邮箱"
    echo "3. Gmail"
    echo "4. 自定义SMTP"
    echo "5. 跳过邮件配置"
    echo ""
    
    while true; do
        read -p "请选择 (1-5): " choice
        case $choice in
            1)
                SMTP_HOST="smtp.qq.com"
                SMTP_PORT="587"
                log_info "已选择QQ邮箱"
                break
                ;;
            2)
                SMTP_HOST="smtp.163.com"
                SMTP_PORT="587"
                log_info "已选择163邮箱"
                break
                ;;
            3)
                SMTP_HOST="smtp.gmail.com"
                SMTP_PORT="587"
                log_info "已选择Gmail"
                break
                ;;
            4)
                read -p "请输入SMTP服务器地址: " SMTP_HOST
                read -p "请输入SMTP端口 (默认587): " SMTP_PORT
                SMTP_PORT=${SMTP_PORT:-587}
                log_info "已配置自定义SMTP: $SMTP_HOST:$SMTP_PORT"
                break
                ;;
            5)
                log_info "跳过邮件配置"
                return 1
                ;;
            *)
                log_error "无效选择，请输入 1-5"
                ;;
        esac
    done
    
    return 0
}

# 配置邮件账户
configure_email_account() {
    log_info "配置邮件账户..."
    
    echo "请输入邮件账户信息:"
    read -p "发件邮箱地址: " SMTP_USER
    
    if [ -z "$SMTP_USER" ]; then
        log_error "邮箱地址不能为空"
        return 1
    fi
    
    read -s -p "邮箱密码或授权码: " SMTP_PASS
    echo
    
    if [ -z "$SMTP_PASS" ]; then
        log_error "密码不能为空"
        return 1
    fi
    
    log_success "邮件账户配置完成"
}

# 更新环境配置文件
update_env_config() {
    log_info "更新环境配置文件..."
    
    local env_file="$PROJECT_DIR/backend/.env"
    
    if [ ! -f "$env_file" ]; then
        log_error "环境配置文件不存在: $env_file"
        return 1
    fi
    
    # 备份原配置文件
    cp "$env_file" "$env_file.backup.$(date +%Y%m%d_%H%M%S)"
    
    # 更新邮件配置
    sed -i "s/^SMTP_HOST=.*/SMTP_HOST=$SMTP_HOST/" "$env_file"
    sed -i "s/^SMTP_PORT=.*/SMTP_PORT=$SMTP_PORT/" "$env_file"
    sed -i "s/^SMTP_USER=.*/SMTP_USER=$SMTP_USER/" "$env_file"
    sed -i "s/^SMTP_PASS=.*/SMTP_PASS=$SMTP_PASS/" "$env_file"
    
    log_success "环境配置文件已更新"
}

# 测试邮件发送
test_email_sending() {
    log_info "测试邮件发送功能..."
    
    # 创建测试脚本
    local test_script="$PROJECT_DIR/test_email.js"
    
    cat > "$test_script" << EOF
const nodemailer = require('nodemailer');
require('dotenv').config({ path: './backend/.env' });

async function testEmail() {
    try {
        // 创建邮件传输器
        const transporter = nodemailer.createTransporter({
            host: process.env.SMTP_HOST,
            port: process.env.SMTP_PORT,
            secure: false,
            auth: {
                user: process.env.SMTP_USER,
                pass: process.env.SMTP_PASS
            }
        });
        
        // 验证连接
        await transporter.verify();
        console.log('✅ SMTP连接验证成功');
        
        // 发送测试邮件
        const info = await transporter.sendMail({
            from: process.env.SMTP_USER,
            to: process.env.SMTP_USER, // 发送给自己
            subject: '王者荣耀代练系统 - 邮件测试',
            text: '这是一封测试邮件，如果您收到此邮件，说明邮件服务配置成功！',
            html: '<h3>邮件测试成功</h3><p>王者荣耀代练管理系统邮件服务已正常工作。</p>'
        });
        
        console.log('✅ 测试邮件发送成功');
        console.log('邮件ID:', info.messageId);
        
    } catch (error) {
        console.error('❌ 邮件测试失败:', error.message);
        process.exit(1);
    }
}

testEmail();
EOF
    
    # 检查是否安装了nodemailer
    cd "$PROJECT_DIR"
    if ! npm list nodemailer &>/dev/null; then
        log_info "安装nodemailer依赖..."
        cd backend
        npm install nodemailer
        cd ..
    fi
    
    # 运行测试
    if node "$test_script"; then
        log_success "邮件发送测试通过"
    else
        log_error "邮件发送测试失败"
        return 1
    fi
    
    # 清理测试文件
    rm -f "$test_script"
}

# 创建邮件服务使用示例
create_email_examples() {
    log_info "创建邮件服务使用示例..."
    
    local examples_dir="$PROJECT_DIR/docs/email-examples"
    mkdir -p "$examples_dir"
    
    # 创建邮件服务使用示例
    cat > "$examples_dir/email-service-usage.md" << 'EOF'
# 邮件服务使用示例

## 基础邮件发送

```typescript
import { EmailService } from '../services/emailService';

const emailService = new EmailService();

// 发送任务完成通知
await emailService.sendTaskCompletionNotification({
  to: '<EMAIL>',
  taskId: 'task_123',
  taskTitle: '王者荣耀段位提升',
  employeeName: '张三',
  completionTime: new Date()
});

// 发送系统报告
await emailService.sendSystemReport({
  to: '<EMAIL>',
  reportType: 'daily',
  data: {
    completedTasks: 15,
    activeEmployees: 8,
    totalRevenue: 1500
  }
});
```

## 邮件模板

### 任务完成通知模板
- 主题: 任务完成通知 - {任务标题}
- 内容: 包含任务详情、完成时间、员工信息

### 系统报告模板
- 主题: 系统日报 - {日期}
- 内容: 包含关键指标、统计数据、异常信息

## 配置说明

邮件服务配置在 `.env` 文件中:
```
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-auth-code
```

## 常见问题

1. **QQ邮箱配置**
   - 需要开启SMTP服务
   - 使用授权码而非登录密码

2. **163邮箱配置**
   - 需要开启客户端授权密码
   - 使用授权密码

3. **Gmail配置**
   - 需要开启两步验证
   - 使用应用专用密码
EOF
    
    log_success "邮件服务使用示例已创建: $examples_dir/email-service-usage.md"
}

# 显示配置结果
show_result() {
    echo -e "${GREEN}"
    echo "=================================================="
    echo "🎉 邮件服务配置完成！"
    echo "=================================================="
    echo -e "${NC}"
    echo "邮件配置信息:"
    echo "  SMTP服务器: $SMTP_HOST"
    echo "  SMTP端口: $SMTP_PORT"
    echo "  发件邮箱: $SMTP_USER"
    echo "  配置文件: $PROJECT_DIR/backend/.env"
    echo ""
    echo "功能说明:"
    echo "  📧 任务完成邮件通知"
    echo "  📊 系统状态报告邮件"
    echo "  🔐 用户验证邮件"
    echo "  📝 密码重置邮件"
    echo ""
    echo "使用方法:"
    echo "  查看示例: cat docs/email-examples/email-service-usage.md"
    echo "  重新测试: node test_email.js"
    echo ""
    echo "注意事项:"
    echo "  - 确保邮箱已开启SMTP服务"
    echo "  - 使用授权码而非登录密码"
    echo "  - 定期检查邮件发送状态"
    echo ""
    echo "重启后端服务以应用邮件配置:"
    echo "  pm2 restart game-boost-backend"
}

# 主函数
main() {
    show_email_info
    
    # 选择邮件服务商
    if ! select_email_provider; then
        log_info "邮件配置已跳过"
        return 0
    fi
    
    # 配置邮件账户
    configure_email_account
    
    # 更新环境配置
    update_env_config
    
    # 测试邮件发送
    read -p "是否测试邮件发送? (y/N): " test_email
    if [[ $test_email =~ ^[Yy]$ ]]; then
        test_email_sending
    fi
    
    # 创建使用示例
    create_email_examples
    
    # 显示结果
    show_result
}

# 运行主函数
main "$@"
