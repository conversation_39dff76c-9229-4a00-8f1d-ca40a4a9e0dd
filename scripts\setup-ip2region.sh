#!/bin/bash

# 🌍 ip2region 离线IP地址库配置脚本
# 王者荣耀代练管理系统专用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 项目配置
PROJECT_DIR="/www/wwwroot/game-boost"

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 显示ip2region信息
show_ip2region_info() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "🌍 ip2region 离线IP地址库配置工具"
    echo "=================================================="
    echo -e "${NC}"
    echo "功能特点:"
    echo "  ✅ 离线IP地理位置查询"
    echo "  ✅ 高性能内存缓存模式"
    echo "  ✅ 包含国家、省份、城市、ISP信息"
    echo "  ✅ 支持用户登录地理位置统计"
    echo "  ✅ 支持在线用户地理分布监控"
    echo ""
}

# 检查ip2region源文件
check_ip2region_source() {
    log_info "检查ip2region源文件..."
    
    local source_dir="$PROJECT_DIR/ip2region-master"
    local data_file="$source_dir/data/ip2region.xdb"
    local nodejs_binding="$source_dir/binding/nodejs"
    
    if [ ! -d "$source_dir" ]; then
        log_error "ip2region源目录不存在: $source_dir"
        log_info "请确保已正确上传ip2region-master目录到项目根目录"
        exit 1
    fi
    
    if [ ! -f "$data_file" ]; then
        log_error "ip2region数据库文件不存在: $data_file"
        exit 1
    fi
    
    if [ ! -d "$nodejs_binding" ]; then
        log_error "ip2region Node.js绑定目录不存在: $nodejs_binding"
        exit 1
    fi
    
    # 检查数据库文件大小
    local file_size=$(stat -c%s "$data_file" 2>/dev/null || stat -f%z "$data_file" 2>/dev/null)
    if [ "$file_size" -lt 1000000 ]; then  # 小于1MB可能有问题
        log_warning "ip2region数据库文件大小异常: ${file_size} bytes"
    else
        log_success "ip2region源文件检查通过 (数据库大小: ${file_size} bytes)"
    fi
}

# 配置ip2region数据库文件
setup_ip2region_database() {
    log_info "配置ip2region数据库文件..."
    
    local source_file="$PROJECT_DIR/ip2region-master/data/ip2region.xdb"
    local target_dir="$PROJECT_DIR/backend/src/data"
    local target_file="$target_dir/ip2region.xdb"
    
    # 创建目标目录
    mkdir -p "$target_dir"
    
    # 复制数据库文件
    cp "$source_file" "$target_file"
    
    # 设置权限
    chmod 644 "$target_file"
    chown www:www "$target_file"
    
    # 验证复制结果
    if [ -f "$target_file" ]; then
        local target_size=$(stat -c%s "$target_file" 2>/dev/null || stat -f%z "$target_file" 2>/dev/null)
        log_success "ip2region数据库文件配置完成 (${target_size} bytes)"
    else
        log_error "ip2region数据库文件复制失败"
        exit 1
    fi
}

# 配置Node.js绑定库
setup_nodejs_binding() {
    log_info "配置ip2region Node.js绑定库..."
    
    local source_dir="$PROJECT_DIR/ip2region-master/binding/nodejs"
    local target_dir="$PROJECT_DIR/backend/src/lib"
    
    # 创建目标目录
    mkdir -p "$target_dir"
    
    # 复制绑定文件
    cp "$source_dir/index.js" "$target_dir/"
    cp "$source_dir/package.json" "$target_dir/"
    
    # 如果存在README，也复制过去
    if [ -f "$source_dir/ReadMe.md" ]; then
        cp "$source_dir/ReadMe.md" "$target_dir/"
    fi
    
    # 设置权限
    chmod 644 "$target_dir"/*
    chown -R www:www "$target_dir"
    
    log_success "ip2region Node.js绑定库配置完成"
}

# 测试ip2region功能
test_ip2region() {
    log_info "测试ip2region功能..."
    
    local test_script="$PROJECT_DIR/test_ip2region.js"
    
    # 创建测试脚本
    cat > "$test_script" << 'EOF'
const ip2region = require('./backend/src/lib/index.js');
const path = require('path');

async function testIp2region() {
    try {
        const dbPath = path.join(__dirname, 'backend/src/data/ip2region.xdb');
        console.log('数据库路径:', dbPath);
        
        // 使用内存缓存模式（推荐）
        const buffer = ip2region.loadContentFromFile(dbPath);
        const searcher = ip2region.newWithBuffer(buffer);
        
        // 测试IP地址
        const testIPs = ['*******', '***************', '*********', '*******'];
        
        console.log('\n=== ip2region 功能测试 ===');
        for (const ip of testIPs) {
            const result = await searcher.search(ip);
            console.log(`IP: ${ip} -> ${result.region} (耗时: ${result.took}μs, IO次数: ${result.ioCount})`);
        }
        
        console.log('\n✅ ip2region 功能测试通过！');
        return true;
    } catch (error) {
        console.error('❌ ip2region 功能测试失败:', error.message);
        return false;
    }
}

testIp2region().then(success => {
    process.exit(success ? 0 : 1);
});
EOF
    
    # 运行测试
    cd "$PROJECT_DIR"
    if node "$test_script"; then
        log_success "ip2region功能测试通过"
    else
        log_error "ip2region功能测试失败"
        log_info "请检查数据库文件和绑定库是否正确配置"
    fi
    
    # 清理测试文件
    rm -f "$test_script"
}

# 创建ip2region服务状态检查脚本
create_status_check() {
    log_info "创建ip2region状态检查脚本..."
    
    local check_script="$PROJECT_DIR/scripts/check-ip2region-status.sh"
    
    cat > "$check_script" << 'EOF'
#!/bin/bash

# ip2region状态检查脚本

PROJECT_DIR="/www/wwwroot/game-boost"
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

echo "🌍 ip2region 状态检查"
echo "======================"

# 检查数据库文件
if [ -f "$PROJECT_DIR/backend/src/data/ip2region.xdb" ]; then
    SIZE=$(stat -c%s "$PROJECT_DIR/backend/src/data/ip2region.xdb" 2>/dev/null || stat -f%z "$PROJECT_DIR/backend/src/data/ip2region.xdb" 2>/dev/null)
    echo -e "${GREEN}✅ 数据库文件存在 (${SIZE} bytes)${NC}"
else
    echo -e "${RED}❌ 数据库文件不存在${NC}"
fi

# 检查绑定库
if [ -f "$PROJECT_DIR/backend/src/lib/index.js" ]; then
    echo -e "${GREEN}✅ Node.js绑定库存在${NC}"
else
    echo -e "${RED}❌ Node.js绑定库不存在${NC}"
fi

# 检查服务状态
if [ -f "$PROJECT_DIR/backend/src/services/ipLocationService.ts" ]; then
    echo -e "${GREEN}✅ IP地理位置服务文件存在${NC}"
else
    echo -e "${RED}❌ IP地理位置服务文件不存在${NC}"
fi

echo ""
echo "如需重新配置，请运行: bash scripts/setup-ip2region.sh"
EOF
    
    chmod +x "$check_script"
    log_success "ip2region状态检查脚本创建完成: $check_script"
}

# 显示配置结果
show_result() {
    echo -e "${GREEN}"
    echo "=================================================="
    echo "🎉 ip2region 配置完成！"
    echo "=================================================="
    echo -e "${NC}"
    echo "配置详情:"
    echo "  📁 数据库文件: backend/src/data/ip2region.xdb"
    echo "  📁 绑定库: backend/src/lib/index.js"
    echo "  📁 服务文件: backend/src/services/ipLocationService.ts"
    echo ""
    echo "功能说明:"
    echo "  🌍 支持离线IP地理位置查询"
    echo "  📊 用户登录地理位置统计"
    echo "  👥 在线用户地理分布监控"
    echo "  🚀 高性能内存缓存模式"
    echo ""
    echo "使用方式:"
    echo "  在后端代码中通过 ipLocationService.getLocation(ip) 查询"
    echo "  返回格式: {country, region, province, city, isp, fullLocation}"
    echo ""
    echo "状态检查:"
    echo "  bash scripts/check-ip2region-status.sh"
    echo ""
    echo "注意事项:"
    echo "  - 数据库文件约11MB，已优化为内存缓存模式"
    echo "  - 查询性能: 单次查询通常在10-50微秒"
    echo "  - 支持IPv4地址，格式: xxx.xxx.xxx.xxx"
}

# 主函数
main() {
    show_ip2region_info
    check_ip2region_source
    setup_ip2region_database
    setup_nodejs_binding
    test_ip2region
    create_status_check
    show_result
}

# 运行主函数
main "$@"
