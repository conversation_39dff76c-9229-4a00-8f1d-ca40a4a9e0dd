#!/bin/bash

# 🔴 Redis 配置和安装脚本
# 王者荣耀代练管理系统 - Redis缓存和队列支持

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 显示Redis信息
show_redis_info() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "🔴 Redis 缓存和队列配置工具"
    echo "=================================================="
    echo -e "${NC}"
    echo "Redis功能:"
    echo "  ✅ 高性能缓存存储"
    echo "  ✅ Bull队列任务处理"
    echo "  ✅ Session存储"
    echo "  ✅ 实时数据缓存"
    echo ""
    echo "注意: Redis是可选组件，不安装不影响基本功能"
    echo ""
}

# 检查Redis是否已安装
check_redis_installation() {
    log_info "检查Redis安装状态..."
    
    if command -v redis-server &> /dev/null; then
        REDIS_VERSION=$(redis-server --version | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1)
        log_success "Redis已安装: $REDIS_VERSION"
        return 0
    else
        log_warning "Redis未安装"
        return 1
    fi
}

# 检查Redis服务状态
check_redis_service() {
    log_info "检查Redis服务状态..."
    
    if systemctl is-active --quiet redis || systemctl is-active --quiet redis-server; then
        log_success "Redis服务运行中"
        return 0
    else
        log_warning "Redis服务未运行"
        return 1
    fi
}

# 测试Redis连接
test_redis_connection() {
    log_info "测试Redis连接..."
    
    if redis-cli ping | grep -q "PONG"; then
        log_success "Redis连接测试成功"
        return 0
    else
        log_error "Redis连接测试失败"
        return 1
    fi
}

# 安装Redis (通过宝塔面板)
install_redis_via_bt() {
    log_info "通过宝塔面板安装Redis..."
    
    echo "请按以下步骤在宝塔面板安装Redis:"
    echo "1. 登录宝塔面板"
    echo "2. 进入 软件商店"
    echo "3. 搜索 'Redis'"
    echo "4. 安装 Redis 7.x 版本"
    echo "5. 启动Redis服务"
    echo ""
    
    read -p "Redis安装完成后按回车继续..." -r
    
    # 重新检查安装状态
    if check_redis_installation; then
        log_success "Redis安装成功"
    else
        log_error "Redis安装失败或未完成"
        return 1
    fi
}

# 配置Redis
configure_redis() {
    log_info "配置Redis..."
    
    # 检查Redis配置文件
    local redis_conf="/etc/redis/redis.conf"
    if [ ! -f "$redis_conf" ]; then
        redis_conf="/etc/redis.conf"
    fi
    
    if [ -f "$redis_conf" ]; then
        log_info "Redis配置文件: $redis_conf"
        
        # 备份原配置
        cp "$redis_conf" "$redis_conf.backup.$(date +%Y%m%d_%H%M%S)"
        
        # 基本配置优化
        log_info "优化Redis配置..."
        
        # 设置最大内存
        if ! grep -q "maxmemory" "$redis_conf"; then
            echo "maxmemory 256mb" >> "$redis_conf"
            echo "maxmemory-policy allkeys-lru" >> "$redis_conf"
        fi
        
        # 启用AOF持久化
        if ! grep -q "appendonly yes" "$redis_conf"; then
            echo "appendonly yes" >> "$redis_conf"
        fi
        
        # 设置密码 (可选)
        read -p "是否设置Redis密码? (y/N): " set_password
        if [[ $set_password =~ ^[Yy]$ ]]; then
            read -s -p "请输入Redis密码: " redis_password
            echo
            if [ -n "$redis_password" ]; then
                sed -i "s/^# requirepass.*/requirepass $redis_password/" "$redis_conf"
                log_success "Redis密码已设置"
                echo "请记住密码: $redis_password"
            fi
        fi
        
        log_success "Redis配置完成"
    else
        log_warning "未找到Redis配置文件，使用默认配置"
    fi
}

# 启动Redis服务
start_redis_service() {
    log_info "启动Redis服务..."
    
    # 尝试不同的服务名
    if systemctl start redis 2>/dev/null; then
        systemctl enable redis
        log_success "Redis服务启动成功 (redis)"
    elif systemctl start redis-server 2>/dev/null; then
        systemctl enable redis-server
        log_success "Redis服务启动成功 (redis-server)"
    else
        log_error "Redis服务启动失败"
        return 1
    fi
}

# 验证Redis功能
verify_redis_functionality() {
    log_info "验证Redis功能..."
    
    # 基本连接测试
    if ! test_redis_connection; then
        return 1
    fi
    
    # 写入测试
    if redis-cli set test_key "test_value" | grep -q "OK"; then
        log_success "Redis写入测试成功"
    else
        log_error "Redis写入测试失败"
        return 1
    fi
    
    # 读取测试
    if [ "$(redis-cli get test_key)" = "test_value" ]; then
        log_success "Redis读取测试成功"
    else
        log_error "Redis读取测试失败"
        return 1
    fi
    
    # 清理测试数据
    redis-cli del test_key > /dev/null
    
    # 队列测试
    if redis-cli lpush test_queue "test_job" | grep -q "1"; then
        redis-cli rpop test_queue > /dev/null
        log_success "Redis队列测试成功"
    else
        log_error "Redis队列测试失败"
        return 1
    fi
    
    log_success "Redis功能验证完成"
}

# 创建Redis监控脚本
create_redis_monitor() {
    log_info "创建Redis监控脚本..."
    
    cat > "/usr/local/bin/redis-monitor.sh" << 'EOF'
#!/bin/bash

# Redis监控脚本

echo "=== Redis状态监控 ==="
echo "时间: $(date)"
echo ""

# 检查服务状态
if systemctl is-active --quiet redis || systemctl is-active --quiet redis-server; then
    echo "✅ Redis服务运行中"
else
    echo "❌ Redis服务未运行"
    exit 1
fi

# 检查连接
if redis-cli ping | grep -q "PONG"; then
    echo "✅ Redis连接正常"
else
    echo "❌ Redis连接失败"
    exit 1
fi

# 内存使用情况
MEMORY_INFO=$(redis-cli info memory | grep used_memory_human)
echo "📊 内存使用: $MEMORY_INFO"

# 连接数
CLIENTS=$(redis-cli info clients | grep connected_clients)
echo "👥 连接数: $CLIENTS"

# 命令统计
COMMANDS=$(redis-cli info stats | grep total_commands_processed)
echo "📈 命令总数: $COMMANDS"

echo ""
echo "详细信息: redis-cli info"
EOF
    
    chmod +x "/usr/local/bin/redis-monitor.sh"
    log_success "Redis监控脚本创建完成: /usr/local/bin/redis-monitor.sh"
}

# 显示配置结果
show_result() {
    echo -e "${GREEN}"
    echo "=================================================="
    echo "🎉 Redis配置完成！"
    echo "=================================================="
    echo -e "${NC}"
    echo "Redis信息:"
    if command -v redis-server &> /dev/null; then
        echo "  版本: $(redis-server --version | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1)"
    fi
    echo "  端口: 6379"
    echo "  配置文件: /etc/redis/redis.conf 或 /etc/redis.conf"
    echo ""
    echo "常用命令:"
    echo "  启动服务: systemctl start redis"
    echo "  停止服务: systemctl stop redis"
    echo "  重启服务: systemctl restart redis"
    echo "  查看状态: systemctl status redis"
    echo "  连接测试: redis-cli ping"
    echo "  监控状态: /usr/local/bin/redis-monitor.sh"
    echo ""
    echo "项目集成:"
    echo "  Redis已配置为可选组件"
    echo "  Bull队列将使用Redis存储"
    echo "  缓存功能将提升系统性能"
    echo ""
    echo "注意事项:"
    echo "  - Redis是可选组件，不影响基本功能"
    echo "  - 建议设置密码以提高安全性"
    echo "  - 定期监控内存使用情况"
}

# 主函数
main() {
    show_redis_info
    
    # 检查是否需要安装Redis
    if ! check_redis_installation; then
        read -p "是否安装Redis? (y/N): " install_redis
        if [[ $install_redis =~ ^[Yy]$ ]]; then
            install_redis_via_bt
        else
            log_info "跳过Redis安装，项目将以无Redis模式运行"
            return 0
        fi
    fi
    
    # 配置Redis
    configure_redis
    
    # 启动服务
    if ! check_redis_service; then
        start_redis_service
    fi
    
    # 验证功能
    verify_redis_functionality
    
    # 创建监控脚本
    create_redis_monitor
    
    # 显示结果
    show_result
}

# 运行主函数
main "$@"
