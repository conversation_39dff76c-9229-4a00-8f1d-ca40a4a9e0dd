#!/usr/bin/env node

/**
 * 测试效率计算修复脚本
 * 验证员工绩效排行榜中效率计算的正确性
 */

const axios = require('axios');

// 配置
const config = {
  baseURL: 'http://localhost:3000/api',
  // 这里需要替换为实际的认证token
  authToken: 'your-auth-token-here'
};

// 测试效率计算
async function testEfficiencyCalculation() {
  try {
    console.log('🧪 开始测试效率计算...\n');

    // 获取员工绩效数据
    const response = await axios.get(`${config.baseURL}/statistics/employee-performance`, {
      headers: {
        'Authorization': `Bearer ${config.authToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.data.success) {
      throw new Error(`API请求失败: ${response.data.message}`);
    }

    const employees = response.data.data;
    console.log(`📊 获取到 ${employees.length} 个员工的绩效数据\n`);

    // 分析每个员工的效率计算
    employees.forEach((employee, index) => {
      const { performance } = employee;
      const {
        totalCommission,
        totalHours,
        efficiency,
        completedTasks,
        totalTasks
      } = performance;

      console.log(`👤 员工 ${index + 1}: ${employee.nickname || employee.username}`);
      console.log(`   总任务数: ${totalTasks}`);
      console.log(`   完成任务: ${completedTasks}`);
      console.log(`   总收益: ¥${totalCommission}`);
      console.log(`   总工时: ${totalHours}h`);
      console.log(`   计算效率: ¥${efficiency}/h`);

      // 验证效率计算
      const expectedEfficiency = totalHours > 0 ? totalCommission / totalHours : 0;
      const isEfficiencyCapped = expectedEfficiency > 1000;
      
      console.log(`   预期效率: ¥${expectedEfficiency.toFixed(2)}/h`);
      
      if (isEfficiencyCapped) {
        console.log(`   ⚠️  效率被限制在1000元/小时以内`);
      }

      // 检查异常情况
      if (totalHours < 0.1 && completedTasks > 0) {
        console.log(`   ⚠️  工时异常低 (${totalHours}h)，可能需要检查数据`);
      }

      if (efficiency > 500) {
        console.log(`   ⚠️  效率较高 (${efficiency}元/h)，请确认数据准确性`);
      }

      console.log('');
    });

    // 统计分析
    const efficiencyStats = employees.map(e => e.performance.efficiency);
    const avgEfficiency = efficiencyStats.reduce((sum, eff) => sum + eff, 0) / efficiencyStats.length;
    const maxEfficiency = Math.max(...efficiencyStats);
    const minEfficiency = Math.min(...efficiencyStats);

    console.log('📈 效率统计分析:');
    console.log(`   平均效率: ¥${avgEfficiency.toFixed(2)}/h`);
    console.log(`   最高效率: ¥${maxEfficiency}/h`);
    console.log(`   最低效率: ¥${minEfficiency}/h`);

    // 检查是否有异常高的效率值
    const highEfficiencyEmployees = employees.filter(e => e.performance.efficiency >= 1000);
    if (highEfficiencyEmployees.length > 0) {
      console.log(`\n⚠️  发现 ${highEfficiencyEmployees.length} 个员工的效率达到上限值:`);
      highEfficiencyEmployees.forEach(emp => {
        console.log(`   - ${emp.nickname || emp.username}: ¥${emp.performance.efficiency}/h`);
      });
    }

    console.log('\n✅ 效率计算测试完成!');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 显示使用说明
function showUsage() {
  console.log(`
🧪 效率计算测试工具

使用方法:
1. 确保后端服务正在运行 (http://localhost:3000)
2. 获取有效的认证token
3. 修改脚本中的 authToken 配置
4. 运行测试: node test-efficiency-calculation.js

测试内容:
- 验证效率计算公式 (总收益 / 总工时)
- 检查效率上限限制 (1000元/h)
- 识别异常的工时数据
- 提供效率统计分析

注意事项:
- 需要有效的认证token才能访问API
- 确保数据库中有员工和任务数据
- 效率计算已优化，避免异常高的数值
`);
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showUsage();
    return;
  }

  if (config.authToken === 'your-auth-token-here') {
    console.log('⚠️  请先配置有效的认证token');
    console.log('💡 提示: 可以从浏览器开发者工具中获取token');
    console.log('');
    showUsage();
    return;
  }

  await testEfficiencyCalculation();
}

// 运行测试
main().catch(error => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});
