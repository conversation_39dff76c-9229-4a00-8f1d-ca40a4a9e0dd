#!/bin/bash

# 🔍 ip2region 部署验证脚本
# 全面验证ip2region离线IP库的部署状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 项目配置
PROJECT_DIR="/www/wwwroot/game-boost"

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 验证计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0

# 检查函数
check_item() {
    local description="$1"
    local condition="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if eval "$condition"; then
        echo -e "${GREEN}✅ $description${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        echo -e "${RED}❌ $description${NC}"
        return 1
    fi
}

# 显示验证信息
show_verification_info() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "🔍 ip2region 部署验证工具"
    echo "=================================================="
    echo -e "${NC}"
    echo "验证项目:"
    echo "  📁 源文件完整性检查"
    echo "  📁 部署文件存在性检查"
    echo "  🔧 功能测试"
    echo "  ⚡ 性能测试"
    echo "  🔐 权限检查"
    echo ""
}

# 检查源文件
verify_source_files() {
    log_info "检查ip2region源文件..."
    
    check_item "ip2region-master目录存在" "[ -d '$PROJECT_DIR/ip2region-master' ]"
    check_item "ip2region数据库源文件存在" "[ -f '$PROJECT_DIR/ip2region-master/data/ip2region.xdb' ]"
    check_item "Node.js绑定源文件存在" "[ -f '$PROJECT_DIR/ip2region-master/binding/nodejs/index.js' ]"
    
    # 检查源数据库文件大小
    if [ -f "$PROJECT_DIR/ip2region-master/data/ip2region.xdb" ]; then
        local source_size=$(stat -c%s "$PROJECT_DIR/ip2region-master/data/ip2region.xdb" 2>/dev/null || stat -f%z "$PROJECT_DIR/ip2region-master/data/ip2region.xdb" 2>/dev/null)
        if [ "$source_size" -gt 10000000 ]; then  # 大于10MB
            check_item "源数据库文件大小正常 (${source_size} bytes)" "true"
        else
            check_item "源数据库文件大小正常 (${source_size} bytes)" "false"
        fi
    fi
}

# 检查部署文件
verify_deployed_files() {
    log_info "检查已部署的ip2region文件..."
    
    check_item "后端数据目录存在" "[ -d '$PROJECT_DIR/backend/src/data' ]"
    check_item "后端lib目录存在" "[ -d '$PROJECT_DIR/backend/src/lib' ]"
    check_item "ip2region数据库文件已部署" "[ -f '$PROJECT_DIR/backend/src/data/ip2region.xdb' ]"
    check_item "ip2region绑定库已部署" "[ -f '$PROJECT_DIR/backend/src/lib/index.js' ]"
    check_item "ip2region包配置文件已部署" "[ -f '$PROJECT_DIR/backend/src/lib/package.json' ]"
    
    # 检查部署文件大小
    if [ -f "$PROJECT_DIR/backend/src/data/ip2region.xdb" ]; then
        local deployed_size=$(stat -c%s "$PROJECT_DIR/backend/src/data/ip2region.xdb" 2>/dev/null || stat -f%z "$PROJECT_DIR/backend/src/data/ip2region.xdb" 2>/dev/null)
        if [ "$deployed_size" -gt 10000000 ]; then  # 大于10MB
            check_item "部署数据库文件大小正常 (${deployed_size} bytes)" "true"
        else
            check_item "部署数据库文件大小正常 (${deployed_size} bytes)" "false"
        fi
    fi
    
    # 检查文件一致性
    if [ -f "$PROJECT_DIR/ip2region-master/data/ip2region.xdb" ] && [ -f "$PROJECT_DIR/backend/src/data/ip2region.xdb" ]; then
        local source_md5=$(md5sum "$PROJECT_DIR/ip2region-master/data/ip2region.xdb" 2>/dev/null | cut -d' ' -f1 || md5 -q "$PROJECT_DIR/ip2region-master/data/ip2region.xdb" 2>/dev/null)
        local deployed_md5=$(md5sum "$PROJECT_DIR/backend/src/data/ip2region.xdb" 2>/dev/null | cut -d' ' -f1 || md5 -q "$PROJECT_DIR/backend/src/data/ip2region.xdb" 2>/dev/null)
        
        if [ "$source_md5" = "$deployed_md5" ]; then
            check_item "源文件与部署文件一致性检查" "true"
        else
            check_item "源文件与部署文件一致性检查" "false"
        fi
    fi
}

# 检查文件权限
verify_permissions() {
    log_info "检查文件权限..."
    
    if [ -f "$PROJECT_DIR/backend/src/data/ip2region.xdb" ]; then
        local file_owner=$(stat -c '%U:%G' "$PROJECT_DIR/backend/src/data/ip2region.xdb" 2>/dev/null || stat -f '%Su:%Sg' "$PROJECT_DIR/backend/src/data/ip2region.xdb" 2>/dev/null)
        check_item "数据库文件所有者正确 ($file_owner)" "[ '$file_owner' = 'www:www' ]"
        
        local file_perms=$(stat -c '%a' "$PROJECT_DIR/backend/src/data/ip2region.xdb" 2>/dev/null || stat -f '%Lp' "$PROJECT_DIR/backend/src/data/ip2region.xdb" 2>/dev/null)
        check_item "数据库文件权限正确 ($file_perms)" "[ '$file_perms' = '644' ]"
    fi
    
    if [ -f "$PROJECT_DIR/backend/src/lib/index.js" ]; then
        local lib_owner=$(stat -c '%U:%G' "$PROJECT_DIR/backend/src/lib/index.js" 2>/dev/null || stat -f '%Su:%Sg' "$PROJECT_DIR/backend/src/lib/index.js" 2>/dev/null)
        check_item "绑定库文件所有者正确 ($lib_owner)" "[ '$lib_owner' = 'www:www' ]"
    fi
}

# 功能测试
verify_functionality() {
    log_info "进行ip2region功能测试..."
    
    if [ ! -f "$PROJECT_DIR/backend/src/data/ip2region.xdb" ] || [ ! -f "$PROJECT_DIR/backend/src/lib/index.js" ]; then
        check_item "ip2region功能测试" "false"
        return 1
    fi
    
    # 创建测试脚本
    local test_script="$PROJECT_DIR/test_ip2region_verify.js"
    cat > "$test_script" << 'EOF'
const ip2region = require('./backend/src/lib/index.js');
const path = require('path');

async function testIp2region() {
    try {
        const dbPath = path.join(__dirname, 'backend/src/data/ip2region.xdb');
        
        // 测试文件加载
        const buffer = ip2region.loadContentFromFile(dbPath);
        if (!buffer || buffer.length < 1000000) {
            throw new Error('数据库文件加载失败或文件过小');
        }
        
        // 创建搜索器
        const searcher = ip2region.newWithBuffer(buffer);
        
        // 测试IP查询
        const testIPs = ['*******', '***************', '*********'];
        let successCount = 0;
        
        for (const ip of testIPs) {
            const result = await searcher.search(ip);
            if (result && result.region && result.region !== '0|0|0|0|0') {
                successCount++;
            }
        }
        
        if (successCount >= 2) {
            console.log('SUCCESS');
        } else {
            console.log('FAIL');
        }
        
    } catch (error) {
        console.log('ERROR:', error.message);
    }
}

testIp2region();
EOF
    
    # 运行测试
    cd "$PROJECT_DIR"
    local test_result=$(timeout 10 node "$test_script" 2>/dev/null | head -1)
    
    if [ "$test_result" = "SUCCESS" ]; then
        check_item "ip2region基础功能测试" "true"
    else
        check_item "ip2region基础功能测试 ($test_result)" "false"
    fi
    
    # 清理测试文件
    rm -f "$test_script"
}

# 性能测试
verify_performance() {
    log_info "进行ip2region性能测试..."
    
    if [ ! -f "$PROJECT_DIR/backend/src/data/ip2region.xdb" ] || [ ! -f "$PROJECT_DIR/backend/src/lib/index.js" ]; then
        check_item "ip2region性能测试" "false"
        return 1
    fi
    
    # 创建性能测试脚本
    local perf_script="$PROJECT_DIR/test_ip2region_perf.js"
    cat > "$perf_script" << 'EOF'
const ip2region = require('./backend/src/lib/index.js');
const path = require('path');

async function perfTest() {
    try {
        const dbPath = path.join(__dirname, 'backend/src/data/ip2region.xdb');
        const buffer = ip2region.loadContentFromFile(dbPath);
        const searcher = ip2region.newWithBuffer(buffer);
        
        // 预热
        await searcher.search('*******');
        
        // 性能测试
        const testCount = 100;
        const startTime = process.hrtime.bigint();
        
        for (let i = 0; i < testCount; i++) {
            await searcher.search('*******');
        }
        
        const endTime = process.hrtime.bigint();
        const avgTime = Number(endTime - startTime) / testCount / 1000; // 微秒
        
        console.log(Math.round(avgTime));
        
    } catch (error) {
        console.log('ERROR');
    }
}

perfTest();
EOF
    
    # 运行性能测试
    cd "$PROJECT_DIR"
    local avg_time=$(timeout 15 node "$perf_script" 2>/dev/null | head -1)
    
    if [[ "$avg_time" =~ ^[0-9]+$ ]] && [ "$avg_time" -lt 100 ]; then
        check_item "ip2region性能测试 (平均${avg_time}μs)" "true"
    else
        check_item "ip2region性能测试 (平均${avg_time}μs)" "false"
    fi
    
    # 清理测试文件
    rm -f "$perf_script"
}

# 检查服务集成
verify_service_integration() {
    log_info "检查服务集成..."
    
    check_item "IP地理位置服务文件存在" "[ -f '$PROJECT_DIR/backend/src/services/ipLocationService.ts' ]"
    
    # 检查服务文件中的ip2region引用
    if [ -f "$PROJECT_DIR/backend/src/services/ipLocationService.ts" ]; then
        if grep -q "require.*lib/index.js" "$PROJECT_DIR/backend/src/services/ipLocationService.ts"; then
            check_item "服务文件正确引用ip2region库" "true"
        else
            check_item "服务文件正确引用ip2region库" "false"
        fi
        
        if grep -q "ip2region.xdb" "$PROJECT_DIR/backend/src/services/ipLocationService.ts"; then
            check_item "服务文件正确引用数据库文件" "true"
        else
            check_item "服务文件正确引用数据库文件" "false"
        fi
    fi
}

# 显示验证结果
show_verification_result() {
    echo ""
    echo -e "${BLUE}=================================================="
    echo "📊 验证结果统计"
    echo "==================================================${NC}"
    
    local success_rate=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
    
    echo "总检查项: $TOTAL_CHECKS"
    echo "通过项: $PASSED_CHECKS"
    echo "失败项: $((TOTAL_CHECKS - PASSED_CHECKS))"
    echo "成功率: ${success_rate}%"
    echo ""
    
    if [ "$success_rate" -ge 90 ]; then
        echo -e "${GREEN}🎉 ip2region部署验证通过！${NC}"
        echo "ip2region离线IP库已正确配置，可以正常使用。"
    elif [ "$success_rate" -ge 70 ]; then
        echo -e "${YELLOW}⚠️  ip2region部署基本正常，但有部分问题${NC}"
        echo "建议检查失败项并进行修复。"
    else
        echo -e "${RED}❌ ip2region部署存在严重问题${NC}"
        echo "请运行修复脚本: bash scripts/setup-ip2region.sh"
    fi
    
    echo ""
    echo "相关命令:"
    echo "  重新配置: bash scripts/setup-ip2region.sh"
    echo "  状态检查: bash scripts/check-ip2region-status.sh"
    echo "  故障排除: cat docs/ip2region故障排除指南.md"
}

# 主函数
main() {
    show_verification_info
    verify_source_files
    echo ""
    verify_deployed_files
    echo ""
    verify_permissions
    echo ""
    verify_functionality
    echo ""
    verify_performance
    echo ""
    verify_service_integration
    show_verification_result
}

# 运行主函数
main "$@"
