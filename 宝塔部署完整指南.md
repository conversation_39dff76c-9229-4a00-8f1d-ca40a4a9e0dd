# 🚀 王者荣耀代练管理系统 - 宝塔部署完整指南

## 📋 项目概述

**项目名称**: 王者荣耀代练任务分发管理系统  
**技术架构**: 前后端分离 + 实时通信  
**部署环境**: 宝塔面板 + Linux服务器  

### 🏗️ 技术栈
- **前端**: Vue 3 + TypeScript + Element Plus + Vite
- **后端**: Node.js 18+ + Express + TypeScript + Prisma ORM
- **数据库**: MySQL 8.0+ (主数据库) + Redis 7+ (缓存，可选)
- **实时通信**: Socket.IO
- **进程管理**: PM2
- **反向代理**: Nginx
- **IP地理位置**: ip2region 离线IP地址库 (高性能本地查询)

## 🛠️ 部署前准备

### 服务器要求
- **CPU**: 2核心以上
- **内存**: 4GB以上 (推荐8GB)
- **硬盘**: 20GB以上可用空间
- **带宽**: 5Mbps以上
- **操作系统**: CentOS 7+ / Ubuntu 18+ / Debian 9+

### 宝塔面板要求
- **宝塔面板**: 7.7.0+
- **必装软件**:
  - Nginx 1.18+
  - MySQL 8.0+
  - Node.js 18.x (通过Node.js版本管理器)
  - PM2管理器
  - Redis 7.x (可选)

## 🚀 快速部署 (推荐)

### 方式一：一键部署脚本

```bash
# 1. 上传项目到服务器
# 2. 进入项目目录
cd /www/wwwroot/game-boost

# 3. 运行一键部署脚本
chmod +x scripts/one-click-deploy.sh
bash scripts/one-click-deploy.sh

# 4. 选择部署模式
# 选择 "3" 进行完整部署
```

### 方式二：分步部署

#### 步骤1: 环境检查
```bash
chmod +x scripts/bt-pre-deployment-check.sh
bash scripts/bt-pre-deployment-check.sh
```

#### 步骤2: 项目配置
```bash
chmod +x scripts/bt-project-config.sh
bash scripts/bt-project-config.sh
```

#### 步骤3: 配置ip2region (可选，已集成在项目配置中)
```bash
chmod +x scripts/setup-ip2region.sh
bash scripts/setup-ip2region.sh
```

#### 步骤3: 启动服务
```bash
cd /www/wwwroot/game-boost
pm2 start ecosystem.config.js
pm2 save
```

## 📝 手动部署详细步骤

### 第一步：安装宝塔面板

```bash
# CentOS 系统
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh

# Ubuntu/Debian 系统
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh
```

### 第二步：安装必要软件

在宝塔面板软件商店安装：
1. **Nginx** (1.18+)
2. **MySQL** (8.0+)
3. **Node.js版本管理器** (安装Node.js 18.x)
4. **PM2管理器**
5. **Redis** (7.x，可选)

### 第三步：创建网站

1. 宝塔面板 → 网站 → 添加站点
2. 域名：`your-domain.com` (或IP地址)
3. 根目录：`/www/wwwroot/game-boost`
4. 选择：纯静态

### 第四步：配置数据库

```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE game_boost_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'game_boost_user'@'localhost' IDENTIFIED BY 'your_secure_password';

-- 授权
GRANT ALL PRIVILEGES ON game_boost_db.* TO 'game_boost_user'@'localhost';
FLUSH PRIVILEGES;
```

### 第五步：上传项目文件

```bash
# 方式1: 通过宝塔文件管理器上传项目压缩包
# 方式2: 使用Git克隆
cd /www/wwwroot/game-boost
git clone https://your-repo-url.git .

# 设置权限
chown -R www:www /www/wwwroot/game-boost
chmod -R 755 /www/wwwroot/game-boost
```

### 第六步：配置后端

```bash
cd /www/wwwroot/game-boost/backend

# 安装依赖
npm install --production

# 创建环境配置
cp .env.example .env
nano .env
```

**环境配置内容** (`.env`):
```env
# 服务器配置
NODE_ENV=production
PORT=3000
HOST=localhost

# 数据库配置
DATABASE_URL="mysql://game_boost_user:your_password@localhost:3306/game_boost_db"

# JWT配置
JWT_SECRET="your-super-secret-jwt-key"
JWT_EXPIRES_IN=7d

# 文件上传配置
UPLOAD_PATH=/www/wwwroot/game-boost/uploads
MAX_FILE_SIZE=10485760

# Socket.IO配置
SOCKET_CORS_ORIGIN=https://your-domain.com

# API配置
API_PREFIX=/api/v1
CORS_ORIGIN=https://your-domain.com
```

```bash
# 初始化数据库
npx prisma generate
npx prisma migrate deploy

# 构建项目
npm run build
```

### 第七步：配置前端

```bash
cd /www/wwwroot/game-boost/frontend

# 安装依赖
npm install

# 创建生产环境配置
cat > .env.production << EOF
VITE_API_BASE_URL=https://your-domain.com/api/v1
VITE_APP_TITLE=王者荣耀代练管理系统
VITE_UPLOAD_URL=https://your-domain.com/api/v1/upload
EOF

# 构建项目
npm run build
```

### 第八步：配置ip2region离线IP库

```bash
cd /www/wwwroot/game-boost

# 检查ip2region源文件
ls -la ip2region-master/data/ip2region.xdb

# 配置ip2region (如果项目配置脚本未包含)
chmod +x scripts/setup-ip2region.sh
bash scripts/setup-ip2region.sh

# 验证配置
ls -la backend/src/data/ip2region.xdb
ls -la backend/src/lib/index.js
```

**ip2region功能说明**:
- 🌍 **离线IP查询**: 无需联网，本地查询IP地理位置
- 🚀 **高性能**: 内存缓存模式，单次查询10-50微秒
- 📊 **详细信息**: 包含国家、省份、城市、ISP信息
- 👥 **应用场景**: 用户登录地理统计、在线用户分布监控

### 第九步：配置PM2

```bash
cd /www/wwwroot/game-boost

# 创建PM2配置文件
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'game-boost-backend',
    script: './backend/dist/index.js',
    cwd: '/www/wwwroot/game-boost',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    restart_delay: 4000
  }]
}
EOF

# 创建必要目录
mkdir -p logs uploads
chown -R www:www logs uploads

# 启动服务
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 第九步：配置Nginx

在宝塔面板 → 网站 → 你的域名 → 设置 → 配置文件，替换为：

```nginx
server {
    listen 80;
    listen 443 ssl http2;
    server_name your-domain.com;
    
    root /www/wwwroot/game-boost/frontend/dist;
    index index.html;
    
    # 前端静态文件
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "public, max-age=31536000";
    }
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 文件上传访问
    location /uploads/ {
        alias /www/wwwroot/game-boost/uploads/;
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }
    
    # 安全配置
    location ~ /\. {
        deny all;
    }
    
    # 文件大小限制
    client_max_body_size 10M;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
```

### 第十步：配置SSL证书

1. 宝塔面板 → 网站 → 你的域名 → SSL
2. 选择 Let's Encrypt
3. 输入域名并申请证书

## ✅ 验证部署

### 检查服务状态
```bash
# 检查PM2进程
pm2 list

# 检查端口占用
netstat -tlnp | grep 3000

# 检查服务状态
systemctl status nginx
systemctl status mysql
```

### 测试访问
```bash
# 测试API
curl http://localhost:3000/api/v1/health

# 测试前端
curl http://your-domain.com
```

### 访问系统
- **前端首页**: `https://your-domain.com`
- **管理后台**: `https://your-domain.com/boss`
- **员工工作台**: `https://your-domain.com/employee`
- **API文档**: `https://your-domain.com/api/v1`

## 🔧 常用管理命令

```bash
# PM2管理
pm2 list                        # 查看进程列表
pm2 logs game-boost-backend     # 查看日志
pm2 restart game-boost-backend  # 重启服务
pm2 stop game-boost-backend     # 停止服务
pm2 monit                       # 监控资源

# Nginx管理
nginx -t                        # 测试配置
nginx -s reload                 # 重载配置
systemctl restart nginx        # 重启Nginx

# 数据库管理
mysql -u game_boost_user -p game_boost_db

# 查看日志
tail -f /www/wwwroot/game-boost/logs/combined.log
```

## 🚨 故障排除

### 常见问题

1. **后端服务无法启动**
   ```bash
   pm2 logs game-boost-backend
   # 检查错误日志，通常是数据库连接或环境配置问题
   ```

2. **前端页面404**
   ```bash
   # 检查Nginx配置和前端构建文件
   ls -la /www/wwwroot/game-boost/frontend/dist/
   nginx -t
   ```

3. **API无法访问**
   ```bash
   # 检查端口占用和防火墙
   netstat -tlnp | grep 3000
   curl http://localhost:3000/api/v1/health
   ```

### 自动诊断
```bash
# 运行完整诊断
bash scripts/deployment-check.sh your-domain.com

# 运行修复脚本
bash scripts/one-click-deploy.sh --repair
```

## 📞 技术支持

如遇部署问题，请提供：
1. 错误日志内容
2. 服务器配置信息  
3. 已完成的部署步骤
4. 运行诊断脚本的结果

---

**祝您部署顺利！** 🎉
