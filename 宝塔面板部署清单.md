# 📋 宝塔面板部署清单

## 🎯 部署前准备

### 1. 服务器要求
- [ ] CPU: 2核心以上
- [ ] 内存: 4GB以上  
- [ ] 硬盘: 20GB以上可用空间
- [ ] 操作系统: CentOS 7+ / Ubuntu 18+ / Debian 9+

### 2. 宝塔面板安装
- [ ] 宝塔面板已安装 (版本 7.7.0+)
- [ ] 可以正常访问面板 (http://服务器IP:8888)

### 3. 必要软件安装
在宝塔面板 → 软件商店安装：
- [ ] **Nginx** (1.18+)
- [ ] **MySQL** (8.0+)
- [ ] **Node.js版本管理器** (安装 Node.js 16.x 或 18.x)
- [ ] **PM2管理器** (进程管理)

## 📦 文件准备和上传

### 1. 准备上传文件
```bash
# 在本地项目目录运行
chmod +x scripts/prepare-upload.sh
bash scripts/prepare-upload.sh
```

### 2. 上传文件到服务器
**方法一：压缩包上传（推荐）**
1. 在宝塔面板 → 网站 → 添加站点
   - 域名：`your-domain.com`
   - 根目录：`/www/wwwroot/game-boost`
2. 上传 `game-boost-upload.tar.gz` 到网站根目录
3. 在宝塔文件管理器中解压文件

**方法二：FTP/SFTP上传**
1. 使用 FTP 工具上传整个 `upload-package` 目录
2. 重命名为 `game-boost`

**方法三：Git克隆（如果有仓库）**
```bash
cd /www/wwwroot
git clone https://your-repo-url.git game-boost
```

### 3. 设置文件权限
```bash
chown -R www:www /www/wwwroot/game-boost
chmod -R 755 /www/wwwroot/game-boost
chmod +x /www/wwwroot/game-boost/scripts/*.sh
```

## 🚀 自动部署（推荐）

### 1. 运行自动部署脚本
```bash
cd /www/wwwroot/game-boost
sudo bash scripts/bt-deploy-helper.sh
```

### 2. 按提示输入配置信息
- [ ] 域名 (例如: example.com)
- [ ] 数据库密码
- [ ] 确认配置信息

### 3. 等待部署完成
脚本会自动完成：
- [ ] 数据库创建和配置
- [ ] 后端依赖安装和构建
- [ ] 前端依赖安装和构建  
- [ ] PM2 进程管理配置
- [ ] Nginx 反向代理配置

## 🔧 手动部署（备选方案）

如果自动部署失败，可以手动执行：

### 1. 配置数据库
```sql
CREATE DATABASE game_boost_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'game_boost_user'@'localhost' IDENTIFIED BY '你的密码';
GRANT ALL PRIVILEGES ON game_boost_db.* TO 'game_boost_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2. 配置后端
```bash
cd /www/wwwroot/game-boost/backend

# 安装依赖
npm install

# 创建环境配置
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等

# 初始化数据库
npx prisma generate
npx prisma migrate deploy

# 构建项目
npm run build
```

### 3. 配置前端
```bash
cd /www/wwwroot/game-boost/frontend

# 安装依赖
npm install

# 创建生产环境配置
cat > .env.production << EOF
VITE_API_BASE_URL=https://your-domain.com/api
VITE_APP_TITLE=游戏代练管理系统
VITE_UPLOAD_URL=https://your-domain.com/api/upload
EOF

# 构建项目
npm run build
```

### 4. 配置 PM2
```bash
cd /www/wwwroot/game-boost
mkdir -p logs
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 5. 配置 Nginx
在宝塔面板 → 网站 → 你的站点 → 设置 → 配置文件，参考部署指南中的配置。

## ✅ 部署验证

### 1. 运行检查脚本
```bash
bash scripts/deployment-check.sh your-domain.com
```

### 2. 手动检查
- [ ] 访问网站首页: `http://your-domain.com`
- [ ] 访问管理后台: `http://your-domain.com/boss`
- [ ] 访问工作台: `http://your-domain.com/employee`
- [ ] 测试 API: `http://your-domain.com/api/health`

### 3. 检查服务状态
```bash
# PM2 进程状态
pm2 list

# Nginx 状态
systemctl status nginx

# MySQL 状态  
systemctl status mysql

# 查看日志
tail -f /www/wwwroot/game-boost/logs/combined.log
```

## 🔒 安全配置

### 1. SSL 证书配置
- [ ] 在宝塔面板 → 网站 → SSL 申请证书
- [ ] 强制 HTTPS 跳转

### 2. 防火墙设置
- [ ] 开放端口: 80, 443
- [ ] 关闭端口: 3000 (后端端口不对外)
- [ ] 修改 SSH 端口

### 3. 定期备份
- [ ] 设置数据库自动备份
- [ ] 设置网站文件备份

## 🚨 常见问题

### 问题1: 后端服务启动失败
```bash
# 查看错误日志
pm2 logs game-boost-backend

# 检查数据库连接
mysql -u game_boost_user -p game_boost_db
```

### 问题2: 前端页面空白
```bash
# 检查构建文件
ls -la /www/wwwroot/game-boost/frontend/dist/

# 检查 Nginx 配置
nginx -t
```

### 问题3: API 请求失败
```bash
# 检查后端服务
curl http://localhost:3000/api/health

# 检查 Nginx 代理
tail -f /www/wwwroot/logs/your-domain.com.error.log
```

## 📞 获取帮助

如果遇到问题：
1. 查看 `docs/宝塔面板故障排除指南.md`
2. 运行 `bash scripts/deployment-check.sh` 获取诊断信息
3. 查看相关日志文件

---

**部署完成后，记得测试所有功能是否正常工作！** 🎉
