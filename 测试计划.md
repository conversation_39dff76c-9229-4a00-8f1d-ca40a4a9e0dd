# 游戏管理与订单管理一致性修复 - 测试计划

## 🎯 测试目标
验证游戏管理模块和订单管理模块的一致性修复是否成功，确保两个模块能够正常协作。

## 📋 测试步骤

### 1. 游戏管理功能测试

#### 1.1 添加游戏测试
- [ ] 访问游戏管理页面 `/admin/games`
- [ ] 点击"添加游戏"按钮
- [ ] 填写游戏信息：
  - 游戏名称（name）：使用英文标识符，如 `lol`
  - 显示名称（displayName）：如 `英雄联盟`
  - 描述：游戏描述信息
  - 图标：上传或输入图标URL
- [ ] 验证表单验证规则是否正确工作
- [ ] 提交表单，确认游戏创建成功
- [ ] 检查创建的游戏数据结构是否正确

#### 1.2 游戏表单字段配置测试
- [ ] 为新创建的游戏添加动态表单字段
- [ ] 测试各种字段类型：
  - TEXT：文本输入框
  - TEXTAREA：多行文本框
  - SELECT：下拉选择框
  - CHECKBOX：多选框
  - NUMBER：数字输入框
  - PASSWORD：密码框
  - IMAGE：图片上传
- [ ] 验证字段键名使用驼峰命名（如 customerName, gameAccount）
- [ ] 确认字段验证规则正确应用

### 2. 订单管理功能测试

#### 2.1 创建订单测试
- [ ] 访问订单创建页面 `/boss/create-order-improved`
- [ ] 选择刚创建的游戏
- [ ] 验证动态表单字段是否正确加载
- [ ] 填写订单信息：
  - 客户姓名（customerName）
  - 游戏账号（gameAccount）
  - 游戏密码（gamePassword）
  - 其他动态字段
- [ ] 检查表单验证是否使用统一的验证规则
- [ ] 提交订单，确认创建成功

#### 2.2 订单数据一致性验证
- [ ] 检查创建的订单数据：
  - `gameType` 字段应该是游戏的 `name`（如 `lol`）
  - `gameId` 字段应该是游戏的ID
  - 动态表单数据正确存储在 `details` 和 `formData` 字段中
- [ ] 验证字段命名使用驼峰命名规范

### 3. 数据一致性测试

#### 3.1 游戏与订单关联测试
- [ ] 在订单列表中查看新创建的订单
- [ ] 确认游戏类型显示正确（显示 displayName）
- [ ] 编辑订单，验证游戏信息正确关联
- [ ] 检查订单详情页面的游戏信息显示

#### 3.2 API响应格式测试
- [ ] 使用浏览器开发者工具检查API响应
- [ ] 验证游戏管理API和订单管理API使用相同的响应格式：
  ```json
  {
    "success": true,
    "data": {...},
    "message": "操作成功",
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
  ```

### 4. 错误处理测试

#### 4.1 表单验证测试
- [ ] 测试必填字段验证
- [ ] 测试字段长度限制
- [ ] 测试字段格式验证（如游戏名称只能包含字母、数字、下划线和连字符）
- [ ] 验证错误消息显示一致

#### 4.2 API错误处理测试
- [ ] 测试重复游戏名称的错误处理
- [ ] 测试无效数据的错误响应
- [ ] 验证错误格式一致性

### 5. 前端状态管理测试

#### 5.1 游戏管理状态测试
- [ ] 验证游戏列表的加载和更新
- [ ] 测试分页功能
- [ ] 检查游戏store的状态管理

#### 5.2 订单管理状态测试
- [ ] 验证订单列表使用订单store
- [ ] 测试订单搜索和筛选功能
- [ ] 检查分页状态管理

## 🔍 关键验证点

### 数据结构一致性
- 游戏的 `name` 字段用作订单的 `gameType`
- 游戏的 `id` 字段用作订单的 `gameId`
- 动态表单字段使用驼峰命名

### 验证规则一致性
- 两个模块使用相同的字段验证规则
- 错误消息格式一致
- 字段命名规范统一

### API格式一致性
- 响应格式统一
- 错误处理机制一致
- 状态码使用规范

## ✅ 测试通过标准

1. 能够成功创建游戏并配置动态表单字段
2. 能够基于游戏创建订单，数据关联正确
3. 字段命名和验证规则在两个模块间保持一致
4. API响应格式统一，错误处理一致
5. 前端状态管理正常工作
6. 数据在数据库中正确存储，字段关联准确

## 🚨 注意事项

1. 测试前确保数据库连接正常
2. 确保前后端服务都已启动
3. 建议在测试环境进行，避免影响生产数据
4. 如发现问题，请记录详细的错误信息和重现步骤
